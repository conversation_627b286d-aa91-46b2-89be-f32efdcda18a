/**
 * Composant de tableau avec drag & drop pour réorganiser les lignes
 */

import React, { useState } from 'react';
import { GripVertical } from 'lucide-react';

interface DragDropRowProps {
  children: React.ReactNode;
  index: number;
  onDragStart: (index: number) => void;
  onDragOver: (index: number) => void;
  onDragEnd: () => void;
  isDragging: boolean;
  isOver: boolean;
}

export const DragDropRow: React.FC<DragDropRowProps> = ({
  children,
  index,
  onDragStart,
  onDragOver,
  onDragEnd,
  isDragging,
  isOver
}) => {
  return (
    <tr
      draggable
      onDragStart={() => onDragStart(index)}
      onDragOver={(e) => {
        e.preventDefault();
        onDragOver(index);
      }}
      onDragEnd={onDragEnd}
      className={`
        hover:bg-blue-50/50 transition-all duration-200
        ${isDragging ? 'opacity-50 bg-blue-100' : ''}
        ${isOver ? 'border-t-2 border-blue-500' : ''}
      `}
    >
      <td className="text-center w-8 px-1">
        <div className="flex items-center justify-center cursor-grab active:cursor-grabbing">
          <GripVertical className="h-4 w-4 text-gray-400 hover:text-gray-600" />
        </div>
      </td>
      {children}
    </tr>
  );
};

interface DragDropTableProps {
  data: any[];
  onReorder: (fromIndex: number, toIndex: number) => void;
  renderRow: (item: any, index: number) => React.ReactNode;
  headers: React.ReactNode;
}

export const DragDropTable: React.FC<DragDropTableProps> = ({
  data,
  onReorder,
  renderRow,
  headers
}) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (index: number) => {
    if (draggedIndex !== null && draggedIndex !== index) {
      setDragOverIndex(index);
    }
  };

  const handleDragEnd = () => {
    if (draggedIndex !== null && dragOverIndex !== null && draggedIndex !== dragOverIndex) {
      onReorder(draggedIndex, dragOverIndex);
    }
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  return (
    <table className="min-w-full table-fixed">
      <thead>
        <tr className="bg-blue-50">
          <th className="text-blue-800 font-medium text-xs w-8 text-center">
            <GripVertical className="h-4 w-4 mx-auto text-gray-400" />
          </th>
          {headers}
        </tr>
      </thead>
      <tbody>
        {data.map((item, index) => (
          <DragDropRow
            key={item.id || index}
            index={index}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}
            isDragging={draggedIndex === index}
            isOver={dragOverIndex === index}
          >
            {renderRow(item, index)}
          </DragDropRow>
        ))}
      </tbody>
    </table>
  );
};
