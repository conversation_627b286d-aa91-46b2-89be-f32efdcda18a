/**
 * Composant principal du tableau global du patrimoine - Version refactorisée
 * Code propre, maintenable et modulaire
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  TrendingUp,
  Euro,
  BarChart3,
  Settings,
  Maximize2,
  Minimize2
} from 'lucide-react';
import type { GlobalPatrimoineTableProps } from '@/types/patrimoine';
import { useGlobalPatrimoineMatrix } from '@/hooks/useGlobalPatrimoineMatrix';
import { PatrimoineMatrix } from './PatrimoineMatrix';
import { FournisseurManager } from './FournisseurManager';
import { PatrimoineEvolutionToggle } from '../PatrimoineEvolutionToggle';
import { PatrimoineAnalytics } from '../PatrimoineAnalytics';
import { formatMontant } from '@/utils/patrimoine-formatters';

/**
 * Composant principal du tableau global du patrimoine refactorisé
 * - Architecture modulaire et maintenable
 * - Séparation claire des responsabilités
 * - Types TypeScript stricts
 * - Logique métier externalisée
 */
export const GlobalPatrimoineTable: React.FC<GlobalPatrimoineTableProps> = ({ 
  isOpen, 
  onClose 
}) => {
  // État local pour l'interface
  const [showFournisseurManager, setShowFournisseurManager] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Hook principal pour la logique métier
  const {
    matrixData,
    editingCell,
    loading,
    startCellEdit,
    updateEditingValue,
    saveCellEdit,
    cancelCellEdit,
    addFournisseur,
    renameFournisseur,
    deleteFournisseur,
    loadData
  } = useGlobalPatrimoineMatrix();

  // Charger les données quand la dialog s'ouvre
  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen, loadData]);

  if (!isOpen) return null;

  return (
    <>
      {/* Dialog principal */}
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent 
          className={`${
            isFullscreen 
              ? 'max-w-[99vw] max-h-[99vh] w-[99vw] h-[99vh]' 
              : 'max-w-[95vw] max-h-[90vh] w-[95vw]'
          } overflow-hidden flex flex-col transition-all duration-300`}
        >
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                Tableau Global du Patrimoine
              </DialogTitle>
            </div>
            
            <DialogDescription>
              Vue d'ensemble de la répartition du patrimoine par client et fournisseur avec totaux consolidés.
            </DialogDescription>
            
            {/* Barre d'actions */}
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => setShowAnalytics(true)}
                  variant="outline"
                  size="sm"
                  className="border-purple-200 text-purple-700 hover:bg-purple-50 shadow-sm"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analyse Approfondie
                </Button>
                
                <Button
                  onClick={() => setShowFournisseurManager(true)}
                  variant="outline"
                  size="sm"
                  className="border-blue-200 text-blue-700 hover:bg-blue-50 shadow-sm"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Gérer Fournisseurs
                </Button>
              </div>
              
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  variant="outline"
                  size="sm"
                  className="border-gray-200 text-gray-700 hover:bg-gray-50 shadow-sm"
                >
                  {isFullscreen ? (
                    <>
                      <Minimize2 className="h-4 w-4 mr-2" />
                      Réduire
                    </>
                  ) : (
                    <>
                      <Maximize2 className="h-4 w-4 mr-2" />
                      Plein Écran
                    </>
                  )}
                </Button>
                
                <Badge 
                  variant="secondary" 
                  className="text-xl px-4 py-2 bg-gradient-to-r from-green-100 to-green-200 text-green-800 font-bold shadow-sm border border-green-300"
                >
                  <Euro className="h-5 w-5 mr-2" />
                  Total: {formatMontant(matrixData.grandTotal)}
                </Badge>
              </div>
            </div>
          </DialogHeader>

          {/* Contenu principal */}
          <div className="flex flex-col h-full min-h-0">
            {/* Matrice du patrimoine */}
            <div className={`overflow-auto flex-shrink-0 ${
              isFullscreen ? 'max-h-[45vh]' : 'max-h-[35vh]'
            } transition-all duration-300`}>
              <PatrimoineMatrix
                matrixData={matrixData}
                editingCell={editingCell}
                onCellEdit={startCellEdit}
                onSaveEdit={saveCellEdit}
                onCancelEdit={cancelCellEdit}
                onEditValueChange={updateEditingValue}
                loading={loading}
                isFullscreen={isFullscreen}
              />
            </div>

            {/* Section d'évolution */}
            <div className="flex-1 min-h-0 border-t border-gray-200 bg-gray-50/50 overflow-auto">
              <PatrimoineEvolutionToggle
                currentTotal={matrixData.grandTotal}
                fournisseurs={matrixData.fournisseurs}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog de gestion des fournisseurs */}
      <FournisseurManager
        isOpen={showFournisseurManager}
        onClose={() => setShowFournisseurManager(false)}
        fournisseurs={matrixData.fournisseurs}
        onAddFournisseur={addFournisseur}
        onRenameFournisseur={renameFournisseur}
        onDeleteFournisseur={deleteFournisseur}
      />

      {/* Dialog d'analyse approfondie */}
      <PatrimoineAnalytics
        isOpen={showAnalytics}
        onClose={() => setShowAnalytics(false)}
      />
    </>
  );
};
