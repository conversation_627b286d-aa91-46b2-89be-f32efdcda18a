/**
 * Hook professionnel pour l'évolution temporelle du patrimoine
 * Système complet avec courbes temps réel pour totaux et fournisseurs
 */

import { useState, useCallback, useMemo, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useSupabaseSubscription, onInsert, onUpdate, onDelete } from './useSupabaseSubscription';
import { usePatrimoineCache } from './usePatrimoineCache';
import { encryptText, safeDecrypt, isEncrypted } from '@/utils/encryption';
import { toast } from 'sonner';

// Types
interface EvolutionSnapshot {
  id: string;
  date: string;
  total: number;
  commentaire?: string;
  created_at: string;
  updated_at: string;
}

interface FournisseurEvolution {
  id: string;
  date: string;
  fournisseurId: string;
  fournisseurNom: string;
  montant: number;
  commentaire?: string;
}

interface ChartDataPoint {
  date: string;
  dateFormatted: string;
  total: number;
  [fournisseurNom: string]: any; // Montants par fournisseur
}

interface EvolutionMetrics {
  totalPoints: number;
  currentTotal: number;
  previousTotal: number;
  evolution: number;
  evolutionPercent: number;
  firstDate?: string;
  lastDate?: string;
}

export const usePatrimoineEvolutionProfessional = () => {
  // États
  const [snapshots, setSnapshots] = useState<EvolutionSnapshot[]>([]);
  const [fournisseurEvolutions, setFournisseurEvolutions] = useState<FournisseurEvolution[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFournisseurs, setSelectedFournisseurs] = useState<string[]>([]);
  const [showTotalCurve, setShowTotalCurve] = useState(true);

  // Hook pour les données de patrimoine actuelles
  const { 
    entries: patrimoineData, 
    fournisseurs: fournisseursCache,
    loadPatrimoineData 
  } = usePatrimoineCache();

  // Charger les snapshots d'évolution globale
  const loadSnapshots = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('patrimoine_evolution')
        .select('*')
        .order('date_snapshot', { ascending: true });

      if (error) throw error;

      const decryptedSnapshots: EvolutionSnapshot[] = await Promise.all(
        (data || []).map(async (snapshot) => {
          const decryptedTotal = isEncrypted(snapshot.total_sous_gestion)
            ? parseFloat(await safeDecrypt(snapshot.total_sous_gestion))
            : parseFloat(snapshot.total_sous_gestion);

          return {
            id: snapshot.id,
            date: snapshot.date_snapshot,
            total: decryptedTotal,
            commentaire: snapshot.commentaire,
            created_at: snapshot.created_at,
            updated_at: snapshot.updated_at
          };
        })
      );

      setSnapshots(decryptedSnapshots);
    } catch (error) {
      console.error('Erreur lors du chargement des snapshots:', error);
      toast.error('Erreur lors du chargement des données d\'évolution');
    }
  }, []);

  // Charger les évolutions par fournisseur
  const loadFournisseurEvolutions = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('patrimoine_evolution_fournisseurs')
        .select('*')
        .order('date_snapshot', { ascending: true });

      if (error) throw error;

      const decryptedEvolutions: FournisseurEvolution[] = await Promise.all(
        (data || []).map(async (evolution) => {
          const decryptedMontant = isEncrypted(evolution.montant_chiffre)
            ? parseFloat(await safeDecrypt(evolution.montant_chiffre))
            : parseFloat(evolution.montant_chiffre);

          // Trouver le nom du fournisseur
          const fournisseur = fournisseursCache?.find(f => f.id === evolution.fournisseur_id);
          const fournisseurNom = fournisseur?.nom || evolution.fournisseur_id;

          return {
            id: evolution.id,
            date: evolution.date_snapshot,
            fournisseurId: evolution.fournisseur_id,
            fournisseurNom,
            montant: decryptedMontant,
            commentaire: evolution.commentaire
          };
        })
      );

      setFournisseurEvolutions(decryptedEvolutions);
    } catch (error) {
      console.error('Erreur lors du chargement des évolutions fournisseurs:', error);
      toast.error('Erreur lors du chargement des évolutions par fournisseur');
    }
  }, [fournisseursCache]);

  // Charger toutes les données
  const loadAllData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadSnapshots(),
        loadFournisseurEvolutions(),
        loadPatrimoineData(false)
      ]);
    } finally {
      setLoading(false);
    }
  }, [loadSnapshots, loadFournisseurEvolutions, loadPatrimoineData]);

  // Créer un snapshot d'évolution
  const createSnapshot = useCallback(async (date: string, total: number, commentaire?: string) => {
    try {
      const encryptedTotal = await encryptText(total.toString());

      const { data, error } = await supabase
        .from('patrimoine_evolution')
        .upsert({
          date_snapshot: date,
          total_sous_gestion: encryptedTotal,
          commentaire: commentaire || null
        }, {
          onConflict: 'date_snapshot'
        })
        .select()
        .single();

      if (error) throw error;

      toast.success('Snapshot d\'évolution créé');
      await loadSnapshots();
      return true;
    } catch (error) {
      console.error('Erreur lors de la création du snapshot:', error);
      toast.error('Erreur lors de la création du snapshot');
      return false;
    }
  }, [loadSnapshots]);

  // Créer une évolution par fournisseur
  const createFournisseurEvolution = useCallback(async (
    date: string, 
    fournisseurId: string, 
    montant: number, 
    commentaire?: string
  ) => {
    try {
      const encryptedMontant = await encryptText(montant.toString());

      const { data, error } = await supabase
        .from('patrimoine_evolution_fournisseurs')
        .upsert({
          date_snapshot: date,
          fournisseur_id: fournisseurId,
          montant_chiffre: encryptedMontant,
          commentaire: commentaire || null
        }, {
          onConflict: 'date_snapshot,fournisseur_id'
        })
        .select()
        .single();

      if (error) throw error;

      await loadFournisseurEvolutions();
      return true;
    } catch (error) {
      console.error('Erreur lors de la création de l\'évolution fournisseur:', error);
      return false;
    }
  }, [loadFournisseurEvolutions]);

  // Figer le total actuel (snapshot automatique)
  const freezeCurrentTotal = useCallback(async () => {
    const today = new Date().toISOString().split('T')[0];
    const currentTotal = patrimoineData?.reduce((sum, entry) => sum + entry.montant, 0) || 0;

    if (currentTotal <= 0) {
      toast.error('Aucun montant à figer');
      return false;
    }

    // Créer le snapshot global
    const snapshotSuccess = await createSnapshot(
      today, 
      currentTotal, 
      `Figé automatiquement le ${new Date().toLocaleDateString('fr-FR')}`
    );

    if (!snapshotSuccess) return false;

    // Créer les évolutions par fournisseur
    let fournisseursCount = 0;
    if (fournisseursCache && patrimoineData) {
      for (const fournisseur of fournisseursCache) {
        const montantFournisseur = patrimoineData
          .filter(entry => entry.fournisseur === fournisseur.nom)
          .reduce((sum, entry) => sum + entry.montant, 0);

        if (montantFournisseur > 0) {
          const success = await createFournisseurEvolution(
            today,
            fournisseur.id,
            montantFournisseur,
            'Figé automatiquement'
          );
          if (success) fournisseursCount++;
        }
      }
    }

    toast.success(
      `Total figé avec succès !\n` +
      `Montant global: ${currentTotal.toLocaleString('fr-FR')}€\n` +
      `Fournisseurs: ${fournisseursCount} enregistrés`
    );

    return true;
  }, [patrimoineData, fournisseursCache, createSnapshot, createFournisseurEvolution]);

  // Données pour les graphiques
  const chartData = useMemo((): ChartDataPoint[] => {
    // Combiner toutes les dates
    const allDates = new Set([
      ...snapshots.map(s => s.date),
      ...fournisseurEvolutions.map(e => e.date)
    ]);

    return Array.from(allDates)
      .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
      .map(date => {
        const snapshot = snapshots.find(s => s.date === date);
        const evolutionsForDate = fournisseurEvolutions.filter(e => e.date === date);

        const result: ChartDataPoint = {
          date,
          dateFormatted: new Date(date).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit'
          }),
          total: snapshot?.total || evolutionsForDate.reduce((sum, e) => sum + e.montant, 0)
        };

        // Ajouter les montants par fournisseur
        evolutionsForDate.forEach(evolution => {
          result[evolution.fournisseurNom] = evolution.montant;
        });

        return result;
      });
  }, [snapshots, fournisseurEvolutions]);

  // Métriques d'évolution
  const metrics = useMemo((): EvolutionMetrics => {
    if (snapshots.length === 0) {
      return {
        totalPoints: 0,
        currentTotal: 0,
        previousTotal: 0,
        evolution: 0,
        evolutionPercent: 0
      };
    }

    const sortedSnapshots = [...snapshots].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const current = sortedSnapshots[sortedSnapshots.length - 1];
    const previous = sortedSnapshots[sortedSnapshots.length - 2];

    const evolution = previous ? current.total - previous.total : 0;
    const evolutionPercent = previous && previous.total > 0 ? (evolution / previous.total) * 100 : 0;

    return {
      totalPoints: snapshots.length,
      currentTotal: current.total,
      previousTotal: previous?.total || 0,
      evolution,
      evolutionPercent,
      firstDate: sortedSnapshots[0]?.date,
      lastDate: current.date
    };
  }, [snapshots]);

  // Actions pour les courbes
  const toggleFournisseur = useCallback((fournisseurNom: string) => {
    setSelectedFournisseurs(prev => {
      const isSelected = prev.includes(fournisseurNom);
      return isSelected 
        ? prev.filter(nom => nom !== fournisseurNom)
        : [...prev, fournisseurNom];
    });
  }, []);

  const selectAllFournisseurs = useCallback(() => {
    const allNoms = fournisseursCache?.map(f => f.nom) || [];
    setSelectedFournisseurs(allNoms);
  }, [fournisseursCache]);

  const clearSelection = useCallback(() => {
    setSelectedFournisseurs([]);
  }, []);

  // Subscriptions temps réel
  useSupabaseSubscription(
    'patrimoine-evolution-professional-realtime',
    [
      // Évolutions globales
      onInsert('patrimoine_evolution', loadSnapshots),
      onUpdate('patrimoine_evolution', loadSnapshots),
      onDelete('patrimoine_evolution', loadSnapshots),

      // Évolutions par fournisseur
      onInsert('patrimoine_evolution_fournisseurs', loadFournisseurEvolutions),
      onUpdate('patrimoine_evolution_fournisseurs', loadFournisseurEvolutions),
      onDelete('patrimoine_evolution_fournisseurs', loadFournisseurEvolutions),

      // Patrimoine actuel
      onInsert('client_patrimoine', () => loadPatrimoineData(false)),
      onUpdate('client_patrimoine', () => loadPatrimoineData(false)),
      onDelete('client_patrimoine', () => loadPatrimoineData(false))
    ]
  );

  // Chargement initial
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  return {
    // Données
    snapshots,
    fournisseurEvolutions,
    chartData,
    metrics,
    fournisseursDisponibles: fournisseursCache || [],
    
    // État
    loading,
    hasData: snapshots.length > 0 || fournisseurEvolutions.length > 0,
    
    // Sélection courbes
    selectedFournisseurs,
    showTotalCurve,
    
    // Actions
    loadAllData,
    createSnapshot,
    createFournisseurEvolution,
    freezeCurrentTotal,
    toggleFournisseur,
    selectAllFournisseurs,
    clearSelection,
    setShowTotalCurve
  };
};
