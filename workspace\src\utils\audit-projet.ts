/**
 * AUDIT COMPLET DU PROJET - VÉRIFICATION ÉLÉMENT PAR ÉLÉMENT
 * Script pour diagnostiquer tous les aspects du projet patrimoine
 */

import { supabase } from '@/integrations/supabase/client';

export interface AuditResult {
  success: boolean;
  message: string;
  details?: any;
}

export interface AuditReport {
  tables: AuditResult[];
  hooks: AuditResult[];
  data: AuditResult[];
  functions: AuditResult[];
  overall: 'SUCCESS' | 'WARNING' | 'ERROR';
}

/**
 * Audit complet du projet
 */
export const auditCompletProjet = async (): Promise<AuditReport> => {
  console.log('🔍 ===== AUDIT COMPLET DU PROJET ===== 🔍');
  
  const report: AuditReport = {
    tables: [],
    hooks: [],
    data: [],
    functions: [],
    overall: 'SUCCESS'
  };

  try {
    // 1. AUDIT DES TABLES SUPABASE
    console.log('\n📋 1. AUDIT DES TABLES SUPABASE');
    
    // Test table patrimoine_evolution
    try {
      const { data, error } = await supabase
        .from('patrimoine_evolution' as any)
        .select('*')
        .limit(1);
      
      if (error) {
        report.tables.push({
          success: false,
          message: `Table patrimoine_evolution: ${error.message}`,
          details: error
        });
        console.error('❌ patrimoine_evolution:', error.message);
      } else {
        report.tables.push({
          success: true,
          message: `Table patrimoine_evolution: OK (${data?.length || 0} entrées testées)`,
          details: data
        });
        console.log('✅ patrimoine_evolution: OK');
      }
    } catch (err) {
      report.tables.push({
        success: false,
        message: `Table patrimoine_evolution: Erreur critique`,
        details: err
      });
      console.error('❌ patrimoine_evolution: Erreur critique');
    }

    // Test table patrimoine_evolution_fournisseurs
    try {
      const { data, error } = await supabase
        .from('patrimoine_evolution_fournisseurs' as any)
        .select('*')
        .limit(1);
      
      if (error) {
        report.tables.push({
          success: false,
          message: `Table patrimoine_evolution_fournisseurs: ${error.message}`,
          details: error
        });
        console.error('❌ patrimoine_evolution_fournisseurs:', error.message);
      } else {
        report.tables.push({
          success: true,
          message: `Table patrimoine_evolution_fournisseurs: OK (${data?.length || 0} entrées testées)`,
          details: data
        });
        console.log('✅ patrimoine_evolution_fournisseurs: OK');
      }
    } catch (err) {
      report.tables.push({
        success: false,
        message: `Table patrimoine_evolution_fournisseurs: Erreur critique`,
        details: err
      });
      console.error('❌ patrimoine_evolution_fournisseurs: Erreur critique');
    }

    // Test tables existantes
    const tablesToTest = [
      'clients',
      'client_patrimoine', 
      'patrimoine_fournisseurs'
    ];

    for (const tableName of tablesToTest) {
      try {
        const { data, error } = await supabase
          .from(tableName as any)
          .select('*')
          .limit(1);
        
        if (error) {
          report.tables.push({
            success: false,
            message: `Table ${tableName}: ${error.message}`,
            details: error
          });
          console.error(`❌ ${tableName}:`, error.message);
        } else {
          report.tables.push({
            success: true,
            message: `Table ${tableName}: OK`,
            details: data
          });
          console.log(`✅ ${tableName}: OK`);
        }
      } catch (err) {
        report.tables.push({
          success: false,
          message: `Table ${tableName}: Erreur critique`,
          details: err
        });
        console.error(`❌ ${tableName}: Erreur critique`);
      }
    }

    // 2. AUDIT DES OPÉRATIONS CRUD
    console.log('\n⚙️ 2. AUDIT DES OPÉRATIONS CRUD');
    
    // Test insertion dans patrimoine_evolution
    try {
      const testDate = new Date().toISOString().split('T')[0];
      const { data, error } = await supabase
        .from('patrimoine_evolution' as any)
        .insert({
          date_snapshot: testDate + '-test',
          total_sous_gestion: 'test-encrypted-data',
          devise: 'EUR',
          commentaire: 'Test audit'
        })
        .select()
        .single();
      
      if (error) {
        report.functions.push({
          success: false,
          message: `Insert patrimoine_evolution: ${error.message}`,
          details: error
        });
        console.error('❌ Insert patrimoine_evolution:', error.message);
      } else {
        // Nettoyer le test
        await supabase
          .from('patrimoine_evolution' as any)
          .delete()
          .eq('id', data.id);
        
        report.functions.push({
          success: true,
          message: `Insert/Delete patrimoine_evolution: OK`,
          details: data
        });
        console.log('✅ Insert/Delete patrimoine_evolution: OK');
      }
    } catch (err) {
      report.functions.push({
        success: false,
        message: `CRUD patrimoine_evolution: Erreur critique`,
        details: err
      });
      console.error('❌ CRUD patrimoine_evolution: Erreur critique');
    }

    // 3. DÉTERMINER LE STATUT GLOBAL
    const hasErrors = [...report.tables, ...report.hooks, ...report.data, ...report.functions]
      .some(result => !result.success);
    
    const hasWarnings = [...report.tables, ...report.hooks, ...report.data, ...report.functions]
      .some(result => result.message.includes('WARNING'));

    if (hasErrors) {
      report.overall = 'ERROR';
    } else if (hasWarnings) {
      report.overall = 'WARNING';
    } else {
      report.overall = 'SUCCESS';
    }

    console.log('\n🎯 ===== RÉSUMÉ AUDIT ===== 🎯');
    console.log(`📊 Tables testées: ${report.tables.length}`);
    console.log(`⚙️ Fonctions testées: ${report.functions.length}`);
    console.log(`🎯 Statut global: ${report.overall}`);
    
    return report;

  } catch (error) {
    console.error('💥 Erreur critique audit:', error);
    report.overall = 'ERROR';
    return report;
  }
};

/**
 * Affiche un résumé de l'audit
 */
export const afficherResumeAudit = (report: AuditReport) => {
  console.log('\n📋 RÉSUMÉ DÉTAILLÉ:');
  
  report.tables.forEach(result => {
    console.log(result.success ? '✅' : '❌', result.message);
  });
  
  report.functions.forEach(result => {
    console.log(result.success ? '✅' : '❌', result.message);
  });
  
  console.log(`\n🎯 STATUT FINAL: ${report.overall}`);
};
