/**
 * Composant de sélection des fournisseurs pour les graphiques d'évolution
 * Interface propre et réutilisable pour la sélection multiple
 */

import React from 'react';
import type { FournisseurSelectorProps } from '@/types/patrimoine';
import { FOURNISSEUR_COLORS } from '@/types/patrimoine';
import { truncateFournisseurName } from '@/utils/patrimoine-formatters';

export const FournisseurSelector: React.FC<FournisseurSelectorProps> = ({
  fournisseurs,
  selectedFournisseurs,
  onFournisseurToggle,
  onSelectAll,
  onClearSelection,
  showTotalCurve,
  onToggleTotalCurve,
  compact = false
}) => {

  if (fournisseurs.length === 0) {
    return null;
  }

  // Version compacte pour les analytics
  if (compact) {
    return (
      <div className="space-y-2">
        {/* En-tête compact */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-xs font-medium text-gray-600">Fournisseurs</span>
            {selectedFournisseurs.length > 0 && (
              <div className="bg-blue-100 text-blue-700 text-xs px-1.5 py-0.5 rounded font-medium">
                {selectedFournisseurs.length}
              </div>
            )}
          </div>

          <div className="flex gap-1 items-center">
            <button
              type="button"
              onClick={onSelectAll}
              className="text-xs py-0.5 px-1.5 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors"
            >
              Tout
            </button>
            <button
              type="button"
              onClick={onClearSelection}
              className="text-xs py-0.5 px-1.5 bg-gray-50 text-gray-600 rounded hover:bg-gray-100 transition-colors"
            >
              Aucun
            </button>
          </div>
        </div>

        {/* Grille compacte - AFFICHAGE DE TOUS LES FOURNISSEURS */}
        <div className="grid grid-cols-4 gap-1">
          {fournisseurs.map((fournisseur, index) => {
            const fournisseurId = fournisseur.nom;
            const isSelected = selectedFournisseurs.includes(fournisseurId);
            const color = FOURNISSEUR_COLORS[index % FOURNISSEUR_COLORS.length];

            return (
              <div
                key={fournisseurId}
                onClick={() => onFournisseurToggle(fournisseurId)}
                className={`flex items-center gap-1 p-1 rounded cursor-pointer transition-all ${
                  isSelected
                    ? 'bg-blue-50 border border-blue-200'
                    : 'hover:bg-gray-50 border border-gray-200/50'
                }`}
              >
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: color }}
                />
                <span className={`text-xs truncate flex-1 ${
                  isSelected ? 'text-blue-700 font-medium' : 'text-gray-600'
                }`}>
                  {truncateFournisseurName(fournisseur.nom, 8)}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // Version complète
  return (
    <div className="space-y-4">
      {/* En-tête avec contrôles */}
      <div className="flex items-center justify-between p-3 bg-gray-50/50 rounded-lg border border-gray-200/50">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
          <span className="text-sm font-medium text-gray-700">Courbes Fournisseurs</span>
          {selectedFournisseurs.length > 0 && (
            <div className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full font-medium">
              {selectedFournisseurs.length} sélectionné{selectedFournisseurs.length > 1 ? 's' : ''}
            </div>
          )}
        </div>

        {/* Actions rapides */}
        <div className="flex gap-2 items-center">
          {/* Toggle courbe totale */}
          <div className="flex items-center gap-1">
            <span className="text-xs text-gray-600">Total</span>
            <button
              onClick={onToggleTotalCurve}
              className={`relative inline-flex h-4 w-7 items-center rounded-full transition-colors ${
                showTotalCurve ? 'bg-gray-600' : 'bg-gray-200'
              }`}
              aria-label="Afficher/masquer la courbe totale"
            >
              <span
                className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                  showTotalCurve ? 'translate-x-3.5' : 'translate-x-0.5'
                }`}
              />
            </button>
          </div>

          <div className="w-px h-4 bg-gray-300"></div>

          <button
            type="button"
            onClick={onSelectAll}
            className="text-xs py-1 px-2 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors"
          >
            Tout
          </button>
          <button
            type="button"
            onClick={onClearSelection}
            className="text-xs py-1 px-2 bg-gray-50 text-gray-600 rounded hover:bg-gray-100 transition-colors"
          >
            Aucun
          </button>
        </div>
      </div>

      {/* Grille des fournisseurs */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
        {fournisseurs.map((fournisseur, index) => {
          const fournisseurId = fournisseur.nom;
          const isSelected = selectedFournisseurs.includes(fournisseurId);
          const color = FOURNISSEUR_COLORS[index % FOURNISSEUR_COLORS.length];

          return (
            <div
              key={fournisseurId}
              onClick={() => onFournisseurToggle(fournisseurId)}
              className={`flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-all duration-200 ${
                isSelected
                  ? 'bg-blue-50 border border-blue-200 shadow-sm'
                  : 'hover:bg-gray-50 border border-gray-200/50'
              }`}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onFournisseurToggle(fournisseurId);
                }
              }}
              aria-pressed={isSelected}
              aria-label={`${isSelected ? 'Désélectionner' : 'Sélectionner'} ${fournisseur.nom}`}
            >
              <div
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  isSelected ? 'scale-110 shadow-md' : 'opacity-60'
                }`}
                style={{ backgroundColor: color }}
              />
              <span className={`text-xs truncate flex-1 transition-colors duration-200 ${
                isSelected ? 'text-blue-700 font-medium' : 'text-gray-600'
              }`}>
                {truncateFournisseurName(fournisseur.nom)}
              </span>
              {isSelected && (
                <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
