/**
 * Hook unifié pour la gestion de l'évolution du patrimoine
 * Combine et simplifie la logique des hooks existants
 */

import { useState, useMemo, useCallback, useEffect } from 'react';
import { usePatrimoineCache } from './usePatrimoineCache';
import { usePatrimoineEvolutionSimple } from './usePatrimoineEvolutionSimple';
import { usePatrimoineEvolutionFournisseurs } from './usePatrimoineEvolutionFournisseurs';
import { useSupabaseSubscription, onInsert, onUpdate, onDelete } from './useSupabaseSubscription';
import type {
  FournisseurData,
  ChartDataPoint,
  EvolutionMetrics
} from '@/types/patrimoine';
import {
  extractFournisseursFromPatrimoine,
  calculateTotalPatrimoine,
  prepareGlobalChartData,
  prepareFournisseursChartData,
  calculateEvolutionMetrics
} from '@/utils/patrimoine-calculations';

/**
 * Hook principal pour l'évolution du patrimoine
 * Centralise toute la logique et les calculs
 */
export const usePatrimoineEvolution = () => {
  // État local
  const [selectedFournisseurs, setSelectedFournisseurs] = useState<string[]>([]);
  const [showTotalCurve, setShowTotalCurve] = useState(true);

  // Hooks de données
  const { 
    entries: patrimoineData, 
    fournisseurs: fournisseursCache, 
    loading: loadingCache,
    loadPatrimoineData 
  } = usePatrimoineCache();

  const {
    snapshots,
    loading: loadingSnapshots,
    hasData
  } = usePatrimoineEvolutionSimple();

  const { getEvolutionsByDate } = usePatrimoineEvolutionFournisseurs();

  // État de chargement global
  const loading = loadingCache || loadingSnapshots;

  // Fournisseurs disponibles (cache ou extraits des données)
  const fournisseursDisponibles = useMemo((): FournisseurData[] => {
    if (fournisseursCache && fournisseursCache.length > 0) {
      return fournisseursCache;
    }

    if (patrimoineData && patrimoineData.length > 0) {
      return extractFournisseursFromPatrimoine(patrimoineData);
    }

    return [];
  }, [fournisseursCache, patrimoineData]);

  // Métriques d'évolution
  const metrics = useMemo((): EvolutionMetrics => {
    return calculateEvolutionMetrics(snapshots);
  }, [snapshots]);

  // Données pour le graphique - COMBINER snapshots + evolutionsByDate
  const chartData = useMemo((): ChartDataPoint[] => {
    const evolutionsByDate = getEvolutionsByDate();

    // Combiner toutes les dates des deux sources
    const allDates = new Set([
      ...snapshots.map(s => s.date),
      ...evolutionsByDate.map(e => e.date)
    ]);

    // Créer les points de données combinés
    const combinedData = Array.from(allDates)
      .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
      .map(date => {
        const snapshot = snapshots.find(s => s.date === date);
        const evolution = evolutionsByDate.find(e => e.date === date);

        const result: ChartDataPoint = {
          date,
          dateFormatted: new Date(date).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit'
          }),
          // Priorité : snapshot.total > evolution.total > 0
          total: snapshot?.total || evolution?.total || 0
        };

        // Ajouter les données par fournisseur si disponibles
        if (evolution?.fournisseurs) {
          Object.entries(evolution.fournisseurs).forEach(([fournisseurId, montant]) => {
            const fournisseur = fournisseursDisponibles.find(f => f.id === fournisseurId);
            if (fournisseur) {
              result[fournisseur.nom] = montant;
            }
          });
        }

        return result;
      });

    return combinedData;
  }, [snapshots, getEvolutionsByDate, fournisseursDisponibles]);

  // Total du patrimoine réel (pour comparaison)
  const totalPatrimoineReel = useMemo(() => {
    return calculateTotalPatrimoine(patrimoineData);
  }, [patrimoineData]);

  // Actions
  const handleFournisseurToggle = useCallback((fournisseurId: string) => {
    setSelectedFournisseurs(prev => {
      const isSelected = prev.includes(fournisseurId);
      if (isSelected) {
        return prev.filter(id => id !== fournisseurId);
      } else {
        return [...prev, fournisseurId];
      }
    });
  }, []);

  const selectAllFournisseurs = useCallback(() => {
    const allIds = fournisseursDisponibles.map(f => f.nom);
    setSelectedFournisseurs(allIds);
  }, [fournisseursDisponibles]);

  const clearSelection = useCallback(() => {
    setSelectedFournisseurs([]);
  }, []);

  const toggleTotalCurve = useCallback(() => {
    setShowTotalCurve(prev => !prev);
  }, []);

  // Chargement initial des données
  const loadData = useCallback(() => {
    loadPatrimoineData(false);
  }, [loadPatrimoineData]);

  // Temps réel : recharger sur changements du patrimoine
  const handlePatrimoineRealtimeUpdate = useCallback(() => {
    console.log('🔄 Patrimoine Evolution: Changement patrimoine détecté, rechargement...');
    loadPatrimoineData(false); // Recharger sans cache
  }, [loadPatrimoineData]);

  // Temps réel : recharger sur changements des évolutions
  const handleEvolutionRealtimeUpdate = useCallback(() => {
    console.log('🔄 Patrimoine Evolution: Changement évolution détecté, rechargement...');
    loadData(); // Recharger les données d'évolution
  }, [loadData]);

  // Subscriptions temps réel
  useSupabaseSubscription(
    'patrimoine-evolution-unified-realtime',
    [
      // Changements dans le patrimoine des clients
      onInsert('client_patrimoine', handlePatrimoineRealtimeUpdate),
      onUpdate('client_patrimoine', handlePatrimoineRealtimeUpdate),
      onDelete('client_patrimoine', handlePatrimoineRealtimeUpdate),

      // Changements dans les évolutions globales
      onInsert('patrimoine_evolution', handleEvolutionRealtimeUpdate),
      onUpdate('patrimoine_evolution', handleEvolutionRealtimeUpdate),
      onDelete('patrimoine_evolution', handleEvolutionRealtimeUpdate),

      // Changements dans les évolutions par fournisseur
      onInsert('patrimoine_evolution_fournisseurs', handleEvolutionRealtimeUpdate),
      onUpdate('patrimoine_evolution_fournisseurs', handleEvolutionRealtimeUpdate),
      onDelete('patrimoine_evolution_fournisseurs', handleEvolutionRealtimeUpdate)
    ]
  );

  return {
    // État
    loading,
    hasData,
    
    // Données
    fournisseursDisponibles,
    chartData,
    metrics,
    totalPatrimoineReel,
    
    // Sélection
    selectedFournisseurs,
    showTotalCurve,
    
    // Actions
    handleFournisseurToggle,
    selectAllFournisseurs,
    clearSelection,
    toggleTotalCurve,
    loadData,
    
    // Informations de debug (à supprimer en production)
    debug: {
      patrimoineDataLength: patrimoineData?.length || 0,
      fournisseursCacheLength: fournisseursCache?.length || 0,
      snapshotsLength: snapshots?.length || 0,
      totalDifference: Math.abs(metrics.currentTotal - totalPatrimoineReel)
    }
  };
};
