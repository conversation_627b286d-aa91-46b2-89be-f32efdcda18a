/**
 * Hook unifié pour la gestion de l'évolution du patrimoine
 * Combine et simplifie la logique des hooks existants
 */

import { useState, useMemo, useCallback, useEffect } from 'react';
import { usePatrimoineCache } from './usePatrimoineCache';
import { usePatrimoineEvolutionSimple } from './usePatrimoineEvolutionSimple';
import { useSupabaseSubscription, onInsert, onUpdate, onDelete } from './useSupabaseSubscription';
import type {
  FournisseurData,
  ChartDataPoint,
  EvolutionMetrics
} from '@/types/patrimoine';
import {
  extractFournisseursFromPatrimoine,
  calculateTotalPatrimoine,
  prepareGlobalChartData,
  prepareFournisseursChartData,
  calculateEvolutionMetrics
} from '@/utils/patrimoine-calculations';

/**
 * Hook principal pour l'évolution du patrimoine
 * Centralise toute la logique et les calculs
 */
export const usePatrimoineEvolution = () => {
  // État local
  const [selectedFournisseurs, setSelectedFournisseurs] = useState<string[]>([]);
  const [showTotalCurve, setShowTotalCurve] = useState(true);

  // Hooks de données
  const { 
    entries: patrimoineData, 
    fournisseurs: fournisseursCache, 
    loading: loadingCache,
    loadPatrimoineData 
  } = usePatrimoineCache();

  const {
    snapshots,
    loading: loadingSnapshots,
    hasData
  } = usePatrimoineEvolutionSimple();

  // État de chargement global
  const loading = loadingCache || loadingSnapshots;

  // Fournisseurs disponibles (cache ou extraits des données)
  const fournisseursDisponibles = useMemo((): FournisseurData[] => {
    if (fournisseursCache && fournisseursCache.length > 0) {
      return fournisseursCache;
    }

    if (patrimoineData && patrimoineData.length > 0) {
      return extractFournisseursFromPatrimoine(patrimoineData);
    }

    return [];
  }, [fournisseursCache, patrimoineData]);

  // Métriques d'évolution
  const metrics = useMemo((): EvolutionMetrics => {
    return calculateEvolutionMetrics(snapshots);
  }, [snapshots]);

  // Données pour le graphique
  const chartData = useMemo((): ChartDataPoint[] => {
    if (selectedFournisseurs.length === 0) {
      return prepareGlobalChartData(snapshots);
    }

    return prepareFournisseursChartData(snapshots, selectedFournisseurs, patrimoineData);
  }, [snapshots, selectedFournisseurs, patrimoineData]);

  // Total du patrimoine réel (pour comparaison)
  const totalPatrimoineReel = useMemo(() => {
    return calculateTotalPatrimoine(patrimoineData);
  }, [patrimoineData]);

  // Actions
  const handleFournisseurToggle = useCallback((fournisseurId: string) => {
    setSelectedFournisseurs(prev => {
      const isSelected = prev.includes(fournisseurId);
      if (isSelected) {
        return prev.filter(id => id !== fournisseurId);
      } else {
        return [...prev, fournisseurId];
      }
    });
  }, []);

  const selectAllFournisseurs = useCallback(() => {
    const allIds = fournisseursDisponibles.map(f => f.nom);
    setSelectedFournisseurs(allIds);
  }, [fournisseursDisponibles]);

  const clearSelection = useCallback(() => {
    setSelectedFournisseurs([]);
  }, []);

  const toggleTotalCurve = useCallback(() => {
    setShowTotalCurve(prev => !prev);
  }, []);

  // Chargement initial des données
  const loadData = useCallback(() => {
    loadPatrimoineData(false);
  }, [loadPatrimoineData]);

  // Temps réel : recharger sur changements du patrimoine
  const handlePatrimoineRealtimeUpdate = useCallback(() => {
    console.log('🔄 Patrimoine Evolution: Changement patrimoine détecté, rechargement...');
    loadPatrimoineData(false); // Recharger sans cache
  }, [loadPatrimoineData]);

  // Temps réel : recharger sur changements des évolutions
  const handleEvolutionRealtimeUpdate = useCallback(() => {
    console.log('🔄 Patrimoine Evolution: Changement évolution détecté, rechargement...');
    loadData(); // Recharger les données d'évolution
  }, [loadData]);

  // Subscriptions temps réel
  useSupabaseSubscription(
    'patrimoine-evolution-unified-realtime',
    [
      // Changements dans le patrimoine des clients
      onInsert('client_patrimoine', handlePatrimoineRealtimeUpdate),
      onUpdate('client_patrimoine', handlePatrimoineRealtimeUpdate),
      onDelete('client_patrimoine', handlePatrimoineRealtimeUpdate),

      // Changements dans les évolutions globales
      onInsert('patrimoine_evolution', handleEvolutionRealtimeUpdate),
      onUpdate('patrimoine_evolution', handleEvolutionRealtimeUpdate),
      onDelete('patrimoine_evolution', handleEvolutionRealtimeUpdate),

      // Changements dans les évolutions par fournisseur
      onInsert('patrimoine_evolution_fournisseurs', handleEvolutionRealtimeUpdate),
      onUpdate('patrimoine_evolution_fournisseurs', handleEvolutionRealtimeUpdate),
      onDelete('patrimoine_evolution_fournisseurs', handleEvolutionRealtimeUpdate)
    ]
  );

  return {
    // État
    loading,
    hasData,
    
    // Données
    fournisseursDisponibles,
    chartData,
    metrics,
    totalPatrimoineReel,
    
    // Sélection
    selectedFournisseurs,
    showTotalCurve,
    
    // Actions
    handleFournisseurToggle,
    selectAllFournisseurs,
    clearSelection,
    toggleTotalCurve,
    loadData,
    
    // Informations de debug (à supprimer en production)
    debug: {
      patrimoineDataLength: patrimoineData?.length || 0,
      fournisseursCacheLength: fournisseursCache?.length || 0,
      snapshotsLength: snapshots?.length || 0,
      totalDifference: Math.abs(metrics.currentTotal - totalPatrimoineReel)
    }
  };
};
