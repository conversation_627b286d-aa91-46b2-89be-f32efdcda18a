/**
 * Composant d'édition inline pour les cellules de la matrice
 * Gère l'affichage et l'édition des valeurs avec validation
 */

import React from 'react';
import { Input } from '@/components/ui/input';
import type { CellEditorProps } from '@/types/patrimoine';

export const CellEditor: React.FC<CellEditorProps> = ({
  isEditing,
  value,
  displayValue,
  onChange,
  onSave,
  onCancel,
  onEdit
}) => {
  if (isEditing) {
    return (
      <Input
        type="number"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-20 text-center text-xs border-blue-300 focus:border-blue-500"
        autoFocus
        onBlur={onSave}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            onSave();
          }
          if (e.key === 'Escape') {
            e.preventDefault();
            onCancel();
          }
        }}
        placeholder="0"
      />
    );
  }

  const hasValue = displayValue !== '-' && displayValue !== '0 €';

  return (
    <span
      className={`cursor-pointer px-2 py-1 rounded text-xs transition-all duration-200 ${
        hasValue
          ? 'text-green-700 hover:bg-green-100 font-bold bg-green-50 border border-green-200'
          : 'text-gray-400 hover:bg-gray-100 border border-gray-200'
      }`}
      onClick={onEdit}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onEdit();
        }
      }}
      aria-label={`Éditer la valeur: ${displayValue}`}
    >
      {displayValue}
    </span>
  );
};
