/**
 * Hook de cache patrimoine optimisé pour les performances
 * Améliore les temps de chargement avec mise en cache intelligente
 */

import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { safeDecrypt, isEncrypted } from '@/utils/encryption';
import { useSupabaseSubscription, onInsert, onUpdate, onDelete } from './useSupabaseSubscription';
import type { PatrimoineEntry, FournisseurData, PatrimoineCacheData } from '@/types/patrimoine';
import { emitPerformanceEvent } from '@/components/ui/PerformanceMonitor';

// Cache plus long pour de meilleures performances
const CACHE_DURATION = 60000; // 1 minute
const STALE_WHILE_REVALIDATE_DURATION = 300000; // 5 minutes

/**
 * Hook de cache patrimoine optimisé
 * - Cache plus intelligent avec stale-while-revalidate
 * - Déchiffrement en arrière-plan
 * - Requêtes parallélisées
 * - Mise en cache des calculs
 */
export const useOptimizedPatrimoineCache = () => {
  const [cache, setCache] = useState<PatrimoineCacheData>({
    entries: [],
    fournisseurs: [],
    lastUpdated: 0,
    loading: false
  });

  const loadingRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cache des calculs pour éviter les recalculs
  const calculatedTotals = useMemo(() => {
    const clientTotals = new Map<string, number>();
    const fournisseurTotals = new Map<string, number>();
    let grandTotal = 0;

    cache.entries.forEach(entry => {
      const montant = entry.montant || 0;
      
      // Total par client
      clientTotals.set(entry.client_id, (clientTotals.get(entry.client_id) || 0) + montant);
      
      // Total par fournisseur
      fournisseurTotals.set(entry.fournisseur, (fournisseurTotals.get(entry.fournisseur) || 0) + montant);
      
      // Grand total
      grandTotal += montant;
    });

    return { clientTotals, fournisseurTotals, grandTotal };
  }, [cache.entries]);

  // Vérifier si le cache est valide
  const isCacheValid = useCallback(() => {
    const now = Date.now();
    return (now - cache.lastUpdated) < CACHE_DURATION && cache.entries.length > 0;
  }, [cache.lastUpdated, cache.entries.length]);

  // Vérifier si on peut utiliser stale-while-revalidate
  const canUseStaleData = useCallback(() => {
    const now = Date.now();
    return (now - cache.lastUpdated) < STALE_WHILE_REVALIDATE_DURATION && cache.entries.length > 0;
  }, [cache.lastUpdated, cache.entries.length]);

  // Déchiffrement optimisé en batch
  const decryptEntriesBatch = useCallback(async (entries: any[]): Promise<PatrimoineEntry[]> => {
    const decryptPromises = entries.map(async (entry) => {
      try {
        // Déchiffrement parallèle des champs sensibles
        const [montantDecrypted, notesDecrypted] = await Promise.all([
          isEncrypted(entry.montant) ? safeDecrypt(entry.montant) : Promise.resolve(entry.montant),
          entry.notes && isEncrypted(entry.notes) ? safeDecrypt(entry.notes) : Promise.resolve(entry.notes)
        ]);

        return {
          ...entry,
          montant: typeof montantDecrypted === 'string' ? parseFloat(montantDecrypted) : montantDecrypted,
          notes: notesDecrypted
        };
      } catch (error) {
        console.warn(`Erreur déchiffrement entrée ${entry.id}:`, error);
        return {
          ...entry,
          montant: 0,
          notes: null
        };
      }
    });

    return Promise.all(decryptPromises);
  }, []);

  // Charger les données avec optimisations
  const loadPatrimoineData = useCallback(async (forceRefresh = false) => {
    const startTime = performance.now();

    // Éviter les requêtes multiples simultanées
    if (loadingRef.current && !forceRefresh) {
      return cache;
    }

    // Annuler la requête précédente si elle existe
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Vérifier le cache
    if (!forceRefresh && isCacheValid()) {
      const duration = performance.now() - startTime;
      emitPerformanceEvent('cache-hit', duration, true);
      return cache;
    }

    // Utiliser les données stale pendant le rechargement
    const useStaleData = !forceRefresh && canUseStaleData();
    if (useStaleData) {
      // Retourner les données stale immédiatement
      // et recharger en arrière-plan
      setTimeout(() => loadPatrimoineData(true), 0);
      const duration = performance.now() - startTime;
      emitPerformanceEvent('stale-data', duration, true);
      return cache;
    }

    try {
      loadingRef.current = true;
      abortControllerRef.current = new AbortController();

      setCache(prev => ({ ...prev, loading: true }));

      // Requêtes parallélisées avec timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout')), 10000); // 10s timeout
      });

      const dataPromise = Promise.all([
        supabase
          .from('client_patrimoine')
          .select('*')
          .order('created_at', { ascending: false })
          .abortSignal(abortControllerRef.current.signal),
        supabase
          .from('patrimoine_fournisseurs')
          .select('*')
          .eq('actif', true)
          .order('ordre_affichage')
          .abortSignal(abortControllerRef.current.signal)
      ]);

      const [patrimoineResponse, fournisseursResponse] = await Promise.race([
        dataPromise,
        timeoutPromise
      ]) as any;

      if (patrimoineResponse.error) throw patrimoineResponse.error;
      if (fournisseursResponse.error) throw fournisseursResponse.error;

      // Déchiffrement en batch pour de meilleures performances
      const decryptedEntries = await decryptEntriesBatch(patrimoineResponse.data || []);

      const newCache = {
        entries: decryptedEntries,
        fournisseurs: fournisseursResponse.data || [],
        lastUpdated: Date.now(),
        loading: false
      };

      setCache(newCache);

      // Émettre l'événement de performance
      const duration = performance.now() - startTime;
      emitPerformanceEvent('cache-miss', duration, false);

      return newCache;

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return cache; // Requête annulée, retourner le cache existant
      }
      
      console.error('❌ Erreur lors du chargement patrimoine:', error);
      setCache(prev => ({ ...prev, loading: false }));
      return cache;
    } finally {
      loadingRef.current = false;
      abortControllerRef.current = null;
    }
  }, [cache, isCacheValid, canUseStaleData, decryptEntriesBatch]);

  // Invalidation intelligente du cache
  const invalidateCache = useCallback(() => {
    setCache(prev => ({ ...prev, lastUpdated: 0 }));
  }, []);

  // Mise à jour optimiste du cache
  const updateCacheEntry = useCallback((entryId: string, newMontant: number) => {
    setCache(prev => ({
      ...prev,
      entries: prev.entries.map(entry =>
        entry.id === entryId ? { ...entry, montant: newMontant } : entry
      ),
      lastUpdated: Date.now() // Marquer comme frais
    }));
  }, []);

  // Ajout optimiste au cache
  const addCacheEntry = useCallback((newEntry: PatrimoineEntry) => {
    setCache(prev => ({
      ...prev,
      entries: [newEntry, ...prev.entries],
      lastUpdated: Date.now()
    }));
  }, []);

  // Suppression optimiste du cache
  const removeCacheEntry = useCallback((entryId: string) => {
    setCache(prev => ({
      ...prev,
      entries: prev.entries.filter(entry => entry.id !== entryId),
      lastUpdated: Date.now()
    }));
  }, []);

  // Préchargement au montage
  useEffect(() => {
    if (cache.entries.length === 0 && !loadingRef.current) {
      loadPatrimoineData();
    }
  }, [loadPatrimoineData]);

  // Gestion temps réel optimisée
  const handleCacheInvalidation = useCallback(() => {
    // Invalidation douce : marquer comme stale mais garder les données
    setCache(prev => ({ ...prev, lastUpdated: prev.lastUpdated - CACHE_DURATION }));
    
    // Recharger en arrière-plan après un délai pour éviter les rafales
    setTimeout(() => {
      if (!loadingRef.current) {
        loadPatrimoineData(true);
      }
    }, 1000);
  }, [loadPatrimoineData]);

  useSupabaseSubscription(
    'optimized-patrimoine-cache',
    [
      onInsert('client_patrimoine', handleCacheInvalidation),
      onUpdate('client_patrimoine', handleCacheInvalidation),
      onDelete('client_patrimoine', handleCacheInvalidation),
      onInsert('patrimoine_fournisseurs', handleCacheInvalidation),
      onUpdate('patrimoine_fournisseurs', handleCacheInvalidation),
      onDelete('patrimoine_fournisseurs', handleCacheInvalidation)
    ]
  );

  // Nettoyage à la destruction
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    // Données
    entries: cache.entries,
    fournisseurs: cache.fournisseurs,
    loading: cache.loading,
    
    // Totaux calculés (mis en cache)
    ...calculatedTotals,
    
    // Actions
    loadPatrimoineData,
    invalidateCache,
    updateCacheEntry,
    addCacheEntry,
    removeCacheEntry,
    
    // État du cache
    isCacheValid: isCacheValid(),
    lastUpdated: cache.lastUpdated
  };
};
