/**
 * Composant principal d'évolution du patrimoine - Version refactorisée
 * Code propre, maintenable et modulaire
 */

import React, { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Calendar, Euro } from 'lucide-react';
import type { PatrimoineEvolutionChartProps } from '@/types/patrimoine';
import { usePatrimoineEvolution } from '@/hooks/usePatrimoineEvolution';
import { FournisseurSelector } from './FournisseurSelector';
import { EvolutionChart } from './EvolutionChart';
import { formatMontant, formatPercentage } from '@/utils/patrimoine-formatters';

/**
 * Composant d'évolution du patrimoine refactorisé
 * - Code modulaire et maintenable
 * - Séparation des responsabilités
 * - Types TypeScript stricts
 * - Pas de code dupliqué
 */
export const PatrimoineEvolutionChart: React.FC<PatrimoineEvolutionChartProps> = ({
  className
}) => {
  const {
    loading,
    hasData,
    fournisseursDisponibles,
    chartData,
    metrics,
    selectedFournisseurs,
    showTotalCurve,
    handleFournisseurToggle,
    selectAllFournisseurs,
    clearSelection,
    toggleTotalCurve,
    loadData
  } = usePatrimoineEvolution();

  // Chargement initial des données
  useEffect(() => {
    loadData();
  }, [loadData]);

  // État de chargement
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Aucune donnée d'évolution
  if (!hasData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-blue-600">
            <Calendar className="h-16 w-16 mx-auto mb-4 text-blue-300" />
            <p className="text-lg font-medium mb-2">Aucun point d'évolution</p>
            <p className="text-sm text-blue-500">
              Ajoutez des points dans le tableau global pour voir l'évolution
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
          <div className="flex items-center gap-3">
            {/* Badge de croissance */}
            {metrics.totalGrowth !== 0 && (
              <Badge
                variant={metrics.totalGrowth >= 0 ? "default" : "destructive"}
                className={
                  metrics.totalGrowth >= 0 
                    ? "bg-blue-100 text-blue-800" 
                    : "bg-red-100 text-red-800"
                }
              >
                {metrics.totalGrowth >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(metrics.totalGrowth)}
              </Badge>
            )}
            
            {/* Badge du total actuel */}
            <Badge variant="outline" className="bg-blue-50 text-blue-800">
              <Euro className="h-3 w-3 mr-1" />
              {formatMontant(metrics.currentTotal)}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Sélecteur de fournisseurs */}
        <FournisseurSelector
          fournisseurs={fournisseursDisponibles}
          selectedFournisseurs={selectedFournisseurs}
          onFournisseurToggle={handleFournisseurToggle}
          onSelectAll={selectAllFournisseurs}
          onClearAll={clearSelection}
          showTotalCurve={showTotalCurve}
          onToggleTotalCurve={toggleTotalCurve}
        />

        {/* Graphique d'évolution */}
        <EvolutionChart
          data={chartData}
          selectedFournisseurs={selectedFournisseurs}
          showTotalCurve={showTotalCurve}
          fournisseurs={fournisseursDisponibles}
          loading={loading}
        />
      </CardContent>
    </Card>
  );
};
