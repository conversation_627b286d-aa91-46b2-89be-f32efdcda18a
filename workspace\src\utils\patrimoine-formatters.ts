/**
 * Utilitaires de formatage pour le patrimoine
 * Centralise toutes les fonctions de formatage pour éviter la duplication
 */

/**
 * Formate un montant en euros
 */
export const formatMontant = (montant: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(montant);
};

/**
 * Formate un pourcentage avec signe
 */
export const formatPercentage = (percentage: number): string => {
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(1)}%`;
};

/**
 * Formate une date pour l'affichage dans les graphiques
 */
export const formatDateForChart = (date: string): string => {
  return new Date(date).toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: '2-digit'
  });
};

/**
 * Formate une date complète
 */
export const formatDateFull = (date: string): string => {
  return new Date(date).toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

/**
 * Formate un montant pour l'axe Y des graphiques
 */
export const formatMontantForAxis = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M€`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(0)}K€`;
  }
  return `${value}€`;
};

/**
 * Tronque un nom de fournisseur pour l'affichage
 */
export const truncateFournisseurName = (nom: string, maxLength: number = 12): string => {
  return nom.length > maxLength ? nom.substring(0, maxLength) + '...' : nom;
};

/**
 * Tronque un nom de fournisseur pour la légende
 */
export const truncateForLegend = (nom: string, maxLength: number = 18): string => {
  return nom.length > maxLength ? nom.substring(0, 15) + '...' : nom;
};
