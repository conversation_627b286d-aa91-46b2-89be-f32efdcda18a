/**
 * Types TypeScript pour le système de patrimoine
 * Centralise toutes les définitions de types pour une meilleure maintenabilité
 */

// Types de base
export interface PatrimoineEntry {
  id: string;
  client_id: string;
  fournisseur: string;
  montant: number;
  devise: string;
  created_at: string;
  updated_at: string;
}

export interface FournisseurData {
  id: string;
  nom: string;
  ordre_affichage: number;
  actif: boolean;
  total?: number; // Total calculé pour l'affichage
}

export interface EvolutionSnapshot {
  id: string;
  date: string; // Format YYYY-MM-DD
  total: number;
  devise: string;
  commentaire?: string;
  created_at: string;
  updated_at: string;
}

export interface FournisseurEvolution {
  id: string;
  date: string;
  fournisseurId: string;
  montant: number;
  devise: string;
  commentaire?: string;
}

// Types pour les métriques
export interface EvolutionMetrics {
  totalGrowth: number;
  averageGrowth: number;
  bestSnapshot: EvolutionSnapshot | null;
  worstSnapshot: EvolutionSnapshot | null;
  currentTotal: number;
  previousTotal: number;
}

// Types pour les données de graphique
export interface ChartDataPoint {
  date: string;
  dateFormatted: string;
  total: number;
  [fournisseurNom: string]: string | number; // Données dynamiques par fournisseur
}

export interface EvolutionByDate {
  date: string;
  fournisseurs: Record<string, number>; // fournisseurId -> montant
  total: number;
}

// Types pour les props des composants
export interface PatrimoineEvolutionChartProps {
  className?: string;
}

export interface FournisseurSelectorProps {
  fournisseurs: FournisseurData[];
  selectedFournisseurs: string[];
  onFournisseurToggle: (fournisseurId: string) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  showTotalCurve: boolean;
  onToggleTotalCurve: () => void;
  compact?: boolean;
}

export interface EvolutionChartProps {
  data: ChartDataPoint[];
  selectedFournisseurs: string[];
  showTotalCurve: boolean;
  fournisseurs: FournisseurData[];
  loading: boolean;
}

// Types pour les hooks
export interface PatrimoineCacheData {
  entries: PatrimoineEntry[];
  fournisseurs: FournisseurData[];
  lastUpdated: number;
  loading: boolean;
}

export interface EvolutionData {
  snapshots: EvolutionSnapshot[];
  loading: boolean;
  error: string | null;
}

// Constantes
export const CACHE_DURATION = 30000; // 30 secondes
export const FOURNISSEUR_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
  '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
] as const;

export type FournisseurColor = typeof FOURNISSEUR_COLORS[number];

// Types pour le tableau global du patrimoine
export interface PatrimoineMatrixEntry {
  clientId: string;
  clientName: string;
  fournisseurId: string;
  fournisseurNom: string;
  montant: number;
  entryId?: string; // ID de l'entrée en base si elle existe
}

export interface MatrixData {
  clients: Array<{
    id: string;
    name: string;
    total: number;
  }>;
  fournisseurs: Array<{
    id: string;
    nom: string;
    total: number;
  }>;
  matrix: Map<string, Map<string, PatrimoineMatrixEntry>>; // clientId -> fournisseurId -> entry
  grandTotal: number;
}

export interface CellEditState {
  clientId: string;
  fournisseurId: string;
  value: string;
}

// Props pour les composants du tableau global
export interface GlobalPatrimoineTableProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface PatrimoineMatrixProps {
  matrixData: MatrixData;
  editingCell: CellEditState | null;
  onCellEdit: (clientId: string, fournisseurId: string, currentValue: number) => void;
  onSaveEdit: () => Promise<void>;
  onCancelEdit: () => void;
  onEditValueChange: (value: string) => void;
  loading: boolean;
  isFullscreen: boolean;
}

export interface FournisseurManagerProps {
  isOpen: boolean;
  onClose: () => void;
  fournisseurs: Array<{
    id: string;
    nom: string;
    total: number;
  }>;
  onAddFournisseur: (name: string) => Promise<boolean>;
  onRenameFournisseur: (id: string, oldName: string, newName: string) => Promise<boolean>;
  onDeleteFournisseur: (id: string, name: string) => Promise<boolean>;
}

export interface CellEditorProps {
  isEditing: boolean;
  value: string;
  displayValue: string;
  onChange: (value: string) => void;
  onSave: () => void;
  onCancel: () => void;
  onEdit: () => void;
}
