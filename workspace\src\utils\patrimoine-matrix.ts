/**
 * Utilitaires pour la gestion de la matrice du patrimoine global
 * Centralise toute la logique de calcul et manipulation de la matrice
 */

import type { 
  PatrimoineEntry, 
  FournisseurData, 
  MatrixData, 
  PatrimoineMatrixEntry 
} from '@/types/patrimoine';
import type { Client } from '@/types';

/**
 * Construit la matrice de patrimoine à partir des données brutes
 */
export const buildPatrimoineMatrix = (
  entries: PatrimoineEntry[],
  clients: Client[],
  fournisseurs: FournisseurData[],
  clientTotals: Map<string, number>,
  fournisseurTotals: Map<string, number>,
  grandTotal: number
): MatrixData => {
  // 1. Initialiser la matrice pour tous les clients
  const matrix = new Map<string, Map<string, PatrimoineMatrixEntry>>();
  
  clients.forEach(client => {
    matrix.set(client.id, new Map());
  });

  // 2. Remplir la matrice avec les données existantes
  entries.forEach(entry => {
    const clientMap = matrix.get(entry.client_id);
    if (clientMap) {
      // Trouver le fournisseur correspondant
      const fournisseur = fournisseurs.find(f => f.nom === entry.fournisseur);
      if (fournisseur) {
        const matrixEntry: PatrimoineMatrixEntry = {
          clientId: entry.client_id,
          clientName: clients.find(c => c.id === entry.client_id)?.name || 'Client inconnu',
          fournisseurId: fournisseur.id,
          fournisseurNom: fournisseur.nom,
          montant: entry.montant,
          entryId: entry.id
        };

        clientMap.set(fournisseur.id, matrixEntry);
      }
    }
  });

  // 3. Préparer les données finales
  const clientsWithTotals = clients.map(client => ({
    id: client.id,
    name: client.name,
    total: clientTotals.get(client.id) || 0
  }));

  const fournisseursWithTotals = fournisseurs.map(fournisseur => ({
    id: fournisseur.id,
    nom: fournisseur.nom,
    total: fournisseurTotals.get(fournisseur.nom) || 0
  }));

  return {
    clients: clientsWithTotals,
    fournisseurs: fournisseursWithTotals,
    matrix,
    grandTotal
  };
};

/**
 * Obtient la valeur d'une cellule de la matrice
 */
export const getCellValue = (
  matrixData: MatrixData,
  clientId: string,
  fournisseurId: string
): number => {
  return matrixData.matrix.get(clientId)?.get(fournisseurId)?.montant || 0;
};

/**
 * Valide une valeur de cellule
 */
export const validateCellValue = (value: string): { isValid: boolean; numericValue: number; error?: string } => {
  if (value.trim() === '') {
    return { isValid: true, numericValue: 0 };
  }

  const numericValue = parseFloat(value);
  
  if (isNaN(numericValue)) {
    return { isValid: false, numericValue: 0, error: 'La valeur doit être un nombre valide' };
  }

  if (numericValue < 0) {
    return { isValid: false, numericValue: 0, error: 'La valeur ne peut pas être négative' };
  }

  return { isValid: true, numericValue };
};

/**
 * Trouve un fournisseur par son ID
 */
export const findFournisseurById = (
  fournisseurs: Array<{ id: string; nom: string; total: number }>,
  fournisseurId: string
): { id: string; nom: string; total: number } | undefined => {
  return fournisseurs.find(f => f.id === fournisseurId);
};

/**
 * Calcule les statistiques de la matrice
 */
export const calculateMatrixStats = (matrixData: MatrixData) => {
  const totalClients = matrixData.clients.length;
  const totalFournisseurs = matrixData.fournisseurs.length;
  const totalCells = totalClients * totalFournisseurs;
  
  let filledCells = 0;
  let totalEntries = 0;

  matrixData.matrix.forEach(clientMap => {
    clientMap.forEach(entry => {
      if (entry.montant > 0) {
        filledCells++;
        totalEntries += entry.montant;
      }
    });
  });

  const fillRate = totalCells > 0 ? (filledCells / totalCells) * 100 : 0;
  const averagePerClient = totalClients > 0 ? matrixData.grandTotal / totalClients : 0;
  const averagePerFournisseur = totalFournisseurs > 0 ? matrixData.grandTotal / totalFournisseurs : 0;

  return {
    totalClients,
    totalFournisseurs,
    totalCells,
    filledCells,
    fillRate,
    averagePerClient,
    averagePerFournisseur,
    grandTotal: matrixData.grandTotal
  };
};

/**
 * Valide le nom d'un fournisseur
 */
export const validateFournisseurName = (
  name: string,
  existingFournisseurs: Array<{ nom: string }>,
  excludeId?: string
): { isValid: boolean; error?: string } => {
  const trimmedName = name.trim();

  if (!trimmedName) {
    return { isValid: false, error: 'Le nom du fournisseur ne peut pas être vide' };
  }

  if (trimmedName.length < 2) {
    return { isValid: false, error: 'Le nom du fournisseur doit contenir au moins 2 caractères' };
  }

  if (trimmedName.length > 50) {
    return { isValid: false, error: 'Le nom du fournisseur ne peut pas dépasser 50 caractères' };
  }

  // Vérifier les doublons (en excluant l'ID si fourni pour les modifications)
  const isDuplicate = existingFournisseurs.some(f => 
    f.nom.toLowerCase() === trimmedName.toLowerCase()
  );

  if (isDuplicate) {
    return { isValid: false, error: 'Un fournisseur avec ce nom existe déjà' };
  }

  return { isValid: true };
};
