/**
 * Composant de graphique d'évolution du patrimoine
 * Affiche les courbes d'évolution avec support multi-fournisseurs
 */

import React from 'react';
import {
  AreaChart,
  Area,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import type { EvolutionChartProps } from '@/types/patrimoine';
import { FOURNISSEUR_COLORS } from '@/types/patrimoine';
import { formatMontant, formatMontantForAxis, truncateForLegend } from '@/utils/patrimoine-formatters';

export const EvolutionChart: React.FC<EvolutionChartProps> = ({
  data,
  selectedFournisseurs,
  showTotalCurve,
  fournisseurs,
  loading
}) => {
  if (loading) {
    return (
      <div className="h-96 bg-gradient-to-br from-gray-50/30 to-blue-50/20 rounded-2xl p-6 border border-gray-100/50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-96 bg-gradient-to-br from-gray-50/30 to-blue-50/20 rounded-2xl p-6 border border-gray-100/50 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <div className="text-lg font-medium mb-2">Aucune donnée d'évolution</div>
          <div className="text-sm">Ajoutez des points d'évolution pour voir le graphique</div>
        </div>
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const sortedPayload = [...payload].sort((a, b) => {
        if (a.dataKey === 'total') return -1;
        if (b.dataKey === 'total') return 1;
        return (b.value as number) - (a.value as number);
      });

      return (
        <div className="bg-white/95 backdrop-blur-xl p-4 border-0 rounded-2xl shadow-2xl max-w-sm">
          <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-100">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <span className="font-semibold text-gray-800 text-sm">{label}</span>
          </div>
          <div className="space-y-2.5">
            {sortedPayload.map((entry, index) => {
              const isTotal = entry.dataKey === 'total';
              const value = entry.value as number;

              if (value === 0 && !isTotal) return null;

              return (
                <div key={`${entry.dataKey}-${index}`} className="flex items-center justify-between gap-4">
                  <div className="flex items-center gap-2.5 min-w-0 flex-1">
                    <div
                      className={`w-3 h-3 rounded-full shadow-sm flex-shrink-0 ${
                        isTotal ? 'ring-2 ring-gray-300 ring-offset-1' : ''
                      }`}
                      style={{ backgroundColor: entry.color }}
                    />
                    <span className={`text-sm truncate ${
                      isTotal ? 'font-bold text-gray-900' : 'font-medium text-gray-700'
                    }`}>
                      {isTotal ? 'Total Global' : entry.dataKey}
                    </span>
                  </div>
                  <span className={`text-sm font-bold whitespace-nowrap ${
                    isTotal ? 'text-gray-900' : 'text-gray-600'
                  }`}>
                    {formatMontant(value)}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="h-96 bg-gradient-to-br from-gray-50/30 to-blue-50/20 rounded-2xl p-6 border border-gray-100/50">
      <ResponsiveContainer width="100%" height="100%">
        {selectedFournisseurs.length > 0 ? (
          <LineChart
            data={data}
            margin={{ top: 30, right: 40, left: 30, bottom: 30 }}
            style={{ filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.05))' }}
          >
            <defs>
              <linearGradient id="gridGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#E5E7EB" stopOpacity={0.8} />
                <stop offset="100%" stopColor="#E5E7EB" stopOpacity={0.2} />
              </linearGradient>
            </defs>
            <CartesianGrid
              strokeDasharray="2 4"
              stroke="url(#gridGradient)"
              strokeWidth={1}
              opacity={0.6}
            />
            <XAxis
              dataKey="dateFormatted"
              stroke="#9CA3AF"
              fontSize={11}
              tickLine={false}
              axisLine={false}
              tick={{ fill: '#6B7280', fontSize: 11 }}
              dy={10}
            />
            <YAxis
              stroke="#9CA3AF"
              fontSize={11}
              tickLine={false}
              axisLine={false}
              tick={{ fill: '#6B7280', fontSize: 11 }}
              dx={-10}
              domain={showTotalCurve ? ['auto', 'auto'] : ['dataMin', 'dataMax']}
              tickFormatter={formatMontantForAxis}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend
              wrapperStyle={{
                paddingTop: '20px',
                fontSize: '12px'
              }}
              iconType="line"
            />

            {/* Ligne du total global */}
            {showTotalCurve && (
              <Line
                type="monotone"
                dataKey="total"
                stroke="#1F2937"
                strokeWidth={4}
                strokeLinecap="round"
                strokeLinejoin="round"
                dot={{
                  fill: '#1F2937',
                  strokeWidth: 0,
                  r: 6,
                  filter: 'drop-shadow(0 2px 4px rgba(31, 41, 55, 0.3))'
                }}
                activeDot={{
                  r: 10,
                  stroke: '#1F2937',
                  strokeWidth: 4,
                  fill: '#ffffff',
                  filter: 'drop-shadow(0 4px 8px rgba(31, 41, 55, 0.4))'
                }}
                name="Total Global"
              />
            )}

            {/* Lignes des fournisseurs sélectionnés */}
            {selectedFournisseurs.map((fournisseurNom, index) => {
              const fournisseur = fournisseurs.find(f => f.nom === fournisseurNom);
              if (!fournisseur) return null;

              const color = FOURNISSEUR_COLORS[index % FOURNISSEUR_COLORS.length];

              return (
                <Line
                  key={fournisseurNom}
                  type="monotone"
                  dataKey={fournisseur.nom}
                  stroke={color}
                  strokeWidth={3}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  dot={{
                    fill: color,
                    strokeWidth: 0,
                    r: 5,
                    filter: `drop-shadow(0 2px 4px ${color}40)`
                  }}
                  activeDot={{
                    r: 8,
                    stroke: color,
                    strokeWidth: 3,
                    fill: '#ffffff',
                    filter: `drop-shadow(0 3px 6px ${color}60)`
                  }}
                  name={truncateForLegend(fournisseur.nom)}
                  connectNulls={false}
                />
              );
            })}
          </LineChart>
        ) : (
          <AreaChart
            data={data}
            margin={{ top: 30, right: 40, left: 30, bottom: 30 }}
            style={{ filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.05))' }}
          >
            <defs>
              <linearGradient id="patrimoineGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.4} />
                <stop offset="50%" stopColor="#3B82F6" stopOpacity={0.1} />
                <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.02} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" opacity={0.5} />
            <XAxis
              dataKey="dateFormatted"
              stroke="#9CA3AF"
              fontSize={11}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="#9CA3AF"
              fontSize={11}
              tickLine={false}
              axisLine={false}
              tickFormatter={formatMontantForAxis}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="total"
              stroke="#3B82F6"
              strokeWidth={4}
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="url(#patrimoineGradient)"
              dot={{
                fill: '#3B82F6',
                strokeWidth: 0,
                r: 6,
                filter: 'drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3))'
              }}
              activeDot={{
                r: 10,
                stroke: '#3B82F6',
                strokeWidth: 4,
                fill: '#ffffff',
                filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.4))'
              }}
            />
          </AreaChart>
        )}
      </ResponsiveContainer>
    </div>
  );
};
