/**
 * Composant graphique professionnel pour l'évolution du patrimoine
 * Courbes temps réel avec sélection multi-fournisseurs
 */

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus, Eye, EyeOff } from 'lucide-react';

// Couleurs pour les courbes de fournisseurs
const FOURNISSEUR_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
  '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
];

interface EvolutionChartProfessionalProps {
  data: any[];
  selectedFournisseurs: string[];
  showTotalCurve: boolean;
  fournisseursDisponibles: any[];
  metrics: any;
  loading: boolean;
  onToggleFournisseur: (nom: string) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onToggleTotalCurve: () => void;
}

export const EvolutionChartProfessional: React.FC<EvolutionChartProfessionalProps> = ({
  data,
  selectedFournisseurs,
  showTotalCurve,
  fournisseursDisponibles,
  metrics,
  loading,
  onToggleFournisseur,
  onSelectAll,
  onClearSelection,
  onToggleTotalCurve
}) => {
  if (loading) {
    return (
      <div className="h-96 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-blue-600 font-medium">Chargement des données d'évolution...</div>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-96 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-100 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <div className="text-lg font-medium mb-2">Aucune donnée d'évolution</div>
          <div className="text-sm">Utilisez "Figer Total Actuel" pour commencer le suivi</div>
        </div>
      </div>
    );
  }

  const formatMontant = (value: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatMontantAxis = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M€`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(0)}k€`;
    }
    return `${value}€`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const sortedPayload = [...payload].sort((a, b) => {
        if (a.dataKey === 'total') return -1;
        if (b.dataKey === 'total') return 1;
        return (b.value as number) - (a.value as number);
      });

      return (
        <div className="bg-white/95 backdrop-blur-xl p-4 border-0 rounded-xl shadow-2xl max-w-sm">
          <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-100">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <span className="font-semibold text-gray-800 text-sm">{label}</span>
          </div>
          <div className="space-y-2">
            {sortedPayload.map((entry, index) => {
              const isTotal = entry.dataKey === 'total';
              const value = entry.value as number;

              if (value === 0 && !isTotal) return null;

              return (
                <div key={`${entry.dataKey}-${index}`} className="flex items-center justify-between gap-4">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <div
                      className={`w-3 h-3 rounded-full shadow-sm flex-shrink-0 ${
                        isTotal ? 'ring-2 ring-gray-300 ring-offset-1' : ''
                      }`}
                      style={{ backgroundColor: entry.color }}
                    />
                    <span className={`text-sm truncate ${
                      isTotal ? 'font-bold text-gray-900' : 'font-medium text-gray-700'
                    }`}>
                      {isTotal ? 'Total Global' : entry.dataKey}
                    </span>
                  </div>
                  <span className={`text-sm font-bold whitespace-nowrap ${
                    isTotal ? 'text-gray-900' : 'text-gray-600'
                  }`}>
                    {formatMontant(value)}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Métriques d'évolution */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-sm text-gray-600 mb-1">Total Actuel</div>
          <div className="text-2xl font-bold text-gray-900">
            {formatMontant(metrics.currentTotal)}
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-sm text-gray-600 mb-1">Évolution</div>
          <div className={`text-2xl font-bold flex items-center gap-2 ${
            metrics.evolution > 0 ? 'text-green-600' : 
            metrics.evolution < 0 ? 'text-red-600' : 'text-gray-600'
          }`}>
            {metrics.evolution > 0 ? <TrendingUp className="h-5 w-5" /> :
             metrics.evolution < 0 ? <TrendingDown className="h-5 w-5" /> :
             <Minus className="h-5 w-5" />}
            {formatMontant(Math.abs(metrics.evolution))}
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-sm text-gray-600 mb-1">Pourcentage</div>
          <div className={`text-2xl font-bold ${
            metrics.evolutionPercent > 0 ? 'text-green-600' : 
            metrics.evolutionPercent < 0 ? 'text-red-600' : 'text-gray-600'
          }`}>
            {metrics.evolutionPercent > 0 ? '+' : ''}{metrics.evolutionPercent.toFixed(1)}%
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-sm text-gray-600 mb-1">Points de Données</div>
          <div className="text-2xl font-bold text-gray-900">
            {metrics.totalPoints}
          </div>
        </div>
      </div>

      {/* Contrôles des courbes */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-3 mb-4">
          <div className="flex items-center gap-2">
            <Button
              onClick={onToggleTotalCurve}
              variant={showTotalCurve ? "default" : "outline"}
              size="sm"
              className="flex items-center gap-2"
            >
              {showTotalCurve ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
              Total Global
            </Button>
          </div>

          <div className="h-6 w-px bg-gray-300"></div>

          <div className="flex items-center gap-2">
            <Button onClick={onSelectAll} variant="outline" size="sm">
              Tout sélectionner
            </Button>
            <Button onClick={onClearSelection} variant="outline" size="sm">
              Tout désélectionner
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {fournisseursDisponibles.map((fournisseur, index) => {
            const isSelected = selectedFournisseurs.includes(fournisseur.nom);
            const color = FOURNISSEUR_COLORS[index % FOURNISSEUR_COLORS.length];
            
            return (
              <Badge
                key={fournisseur.id}
                variant={isSelected ? "default" : "outline"}
                className={`cursor-pointer transition-all hover:scale-105 ${
                  isSelected ? 'shadow-md' : 'hover:bg-gray-50'
                }`}
                style={isSelected ? { backgroundColor: color, borderColor: color } : {}}
                onClick={() => onToggleFournisseur(fournisseur.nom)}
              >
                {fournisseur.nom}
              </Badge>
            );
          })}
        </div>
      </div>

      {/* Graphique */}
      <div className="h-96 bg-gradient-to-br from-gray-50/30 to-blue-50/20 rounded-xl p-6 border border-gray-100">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ top: 30, right: 40, left: 30, bottom: 30 }}
          >
            <defs>
              <linearGradient id="gridGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#E5E7EB" stopOpacity={0.8} />
                <stop offset="100%" stopColor="#E5E7EB" stopOpacity={0.2} />
              </linearGradient>
            </defs>
            
            <CartesianGrid
              strokeDasharray="2 4"
              stroke="url(#gridGradient)"
              strokeWidth={1}
              opacity={0.6}
            />
            
            <XAxis
              dataKey="dateFormatted"
              stroke="#9CA3AF"
              fontSize={11}
              tickLine={false}
              axisLine={false}
              tick={{ fill: '#6B7280', fontSize: 11 }}
              dy={10}
            />
            
            <YAxis
              stroke="#9CA3AF"
              fontSize={11}
              tickLine={false}
              axisLine={false}
              tick={{ fill: '#6B7280', fontSize: 11 }}
              dx={-10}
              tickFormatter={formatMontantAxis}
            />
            
            <Tooltip content={<CustomTooltip />} />
            <Legend
              wrapperStyle={{
                paddingTop: '20px',
                fontSize: '12px'
              }}
              iconType="line"
            />

            {/* Courbe du total global */}
            {showTotalCurve && (
              <Line
                type="monotone"
                dataKey="total"
                stroke="#1F2937"
                strokeWidth={4}
                strokeLinecap="round"
                strokeLinejoin="round"
                dot={{
                  fill: '#1F2937',
                  strokeWidth: 0,
                  r: 6
                }}
                activeDot={{
                  r: 10,
                  stroke: '#1F2937',
                  strokeWidth: 4,
                  fill: '#ffffff'
                }}
                name="Total Global"
              />
            )}

            {/* Courbes des fournisseurs sélectionnés */}
            {selectedFournisseurs.map((fournisseurNom, index) => {
              const color = FOURNISSEUR_COLORS[index % FOURNISSEUR_COLORS.length];
              
              return (
                <Line
                  key={fournisseurNom}
                  type="monotone"
                  dataKey={fournisseurNom}
                  stroke={color}
                  strokeWidth={3}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  dot={{
                    fill: color,
                    strokeWidth: 0,
                    r: 5
                  }}
                  activeDot={{
                    r: 8,
                    stroke: color,
                    strokeWidth: 3,
                    fill: '#ffffff'
                  }}
                  name={fournisseurNom}
                  connectNulls={false}
                />
              );
            })}
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
