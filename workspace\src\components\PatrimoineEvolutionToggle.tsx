import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ChevronDown, ChevronUp, Save, Trash2, Plus, GripVertical } from 'lucide-react';
import { toast } from 'sonner';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { usePatrimoineEvolutionFournisseurs } from '@/hooks/usePatrimoineEvolutionFournisseurs';
import { supabase } from '@/integrations/supabase/client';
import { auditCompletProjet, afficherResumeAudit } from '@/utils/audit-projet';

// Type pour les données de ligne du tableau
interface RowData {
  date: string | null;
  snapshot: any;
  evolution: any;
  fournisseurMontants: Record<string, number>;
  totalCalcule: number;
  totalSnapshot: number;
  isEmptyRow: boolean;
  emptyRowId?: string;
}

interface PatrimoineEvolutionToggleProps {
  currentTotal: number;
  fournisseurs: Array<{ id: string; nom: string; total: number }>;
}

export const PatrimoineEvolutionToggle: React.FC<PatrimoineEvolutionToggleProps> = ({
  currentTotal,
  fournisseurs
}) => {
  const [showEvolutionTable, setShowEvolutionTable] = useState(false);
  const [editingCell, setEditingCell] = useState<{
    index: number;
    date: string;
    fournisseurId?: string;
    field: 'date' | 'total' | 'fournisseur';
    value: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [customOrder, setCustomOrder] = useState<string[]>([]);
  const [showEmptyRows, setShowEmptyRows] = useState<boolean>(true); // Contrôle simple pour afficher/masquer les lignes vides


  const {
    snapshots,
    upsertSnapshot,
    deleteSnapshot,
    loadSnapshots,
    formatMontant: formatMontantEvolution
  } = usePatrimoineEvolutionSimple();

  const {
    evolutions,
    upsertEvolution,
    deleteEvolution,
    loadEvolutions,
    getEvolutionsByDate,
    formatMontant: formatMontantFournisseur,
    loading: loadingFournisseurs,
    error: errorFournisseurs
  } = usePatrimoineEvolutionFournisseurs();

  const handleSaveEdit = async () => {
    if (!editingCell || isLoading) return;

    setIsLoading(true);
    try {
      if (editingCell.field === 'date') {
        if (!editingCell.value) {
          toast.error('Veuillez sélectionner une date');
          return;
        }

        // Sauvegarder la nouvelle date pour le snapshot existant
        const currentSnapshot = snapshots.find(s => s.date === editingCell.date);
        if (currentSnapshot) {
          const success = await upsertSnapshot(
            editingCell.value, // nouvelle date
            currentSnapshot.total
          );

          if (success) {
            // Supprimer l'ancien snapshot si la date a changé
            if (editingCell.value !== editingCell.date) {
              await deleteSnapshot(currentSnapshot.id);
            }
            setEditingCell(null);
            toast.success('Date mise à jour avec succès');
          }
        } else {
          // Créer un nouveau snapshot avec la date sélectionnée
          const success = await upsertSnapshot(editingCell.value, 0);
          if (success) {
            setEditingCell(null);
            toast.success('Nouvelle ligne créée');
          }
        }
        return;
      }

      if (editingCell.field === 'fournisseur' && editingCell.fournisseurId) {
        const montant = parseFloat(editingCell.value);
        if (isNaN(montant) || montant < 0) {
          toast.error('Le montant doit être un nombre positif valide');
          return;
        }

        const success = await upsertEvolution(
          editingCell.date,
          editingCell.fournisseurId,
          montant
        );

        if (success) {
          setEditingCell(null);
          toast.success('Montant sauvegardé avec succès');
        }
        return;
      }

      if (editingCell.field === 'total') {
        const total = parseFloat(editingCell.value);
        if (isNaN(total) || total < 0) {
          toast.error('Le total doit être un nombre positif valide');
          return;
        }

        const success = await upsertSnapshot(
          editingCell.date,
          total
        );

        if (success) {
          setEditingCell(null);
          toast.success('Total sauvegardé avec succès');
        }
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast.error('Erreur lors de la sauvegarde');
    } finally {
      setIsLoading(false);
    }
  };

  // AUDIT COMPLET DU PROJET
  const lancerAuditComplet = async () => {
    try {
      console.log('🔍 Lancement de l\'audit complet...');
      toast.info('Audit en cours... Voir la console');

      const report = await auditCompletProjet();
      afficherResumeAudit(report);

      if (report.overall === 'SUCCESS') {
        toast.success('✅ Audit terminé - Tout fonctionne parfaitement !');
      } else if (report.overall === 'WARNING') {
        toast.warning('⚠️ Audit terminé - Quelques avertissements détectés');
      } else {
        toast.error('❌ Audit terminé - Erreurs détectées, voir la console');
      }

      // Audit spécifique à ce composant
      console.log('\n🎯 AUDIT COMPOSANT SPÉCIFIQUE:');
      console.log('✅ Snapshots chargés:', snapshots.length);
      console.log('✅ Évolutions chargées:', evolutions.length);
      console.log('✅ Fournisseurs disponibles:', fournisseurs.length);
      console.log('✅ État interface:', {
        tableAffichee: showEvolutionTable,
        lignesVidesAffichees: showEmptyRows,
        ordrePersonnalise: customOrder.length,
        enEdition: !!editingCell,
        chargement: isLoading
      });

    } catch (error) {
      console.error('💥 Erreur audit:', error);
      toast.error('Erreur lors de l\'audit');
    }
  };

  const handleDeleteEntireLine = async (date: string) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer toute la ligne du ${new Date(date).toLocaleDateString('fr-FR')} ?\n\nCela supprimera :\n- Le snapshot global\n- Tous les montants par fournisseur`)) {
      return;
    }

    setIsLoading(true);

    try {
      let suppressionsReussies = 0;
      let suppressionsEchouees = 0;

      // 1. Supprimer le snapshot global s'il existe
      const snapshot = snapshots.find(s => s.date === date);
      if (snapshot) {
        const snapshotDeleted = await deleteSnapshot(snapshot.id);
        if (snapshotDeleted) {
          suppressionsReussies++;
        } else {
          suppressionsEchouees++;
        }
      }

      // 2. Supprimer toutes les évolutions de fournisseurs pour cette date
      const evolutionsToDelete = evolutions.filter(e => e.date === date);

      for (const evolution of evolutionsToDelete) {
        const evolutionDeleted = await deleteEvolution(evolution.id);
        if (evolutionDeleted) {
          suppressionsReussies++;
        } else {
          suppressionsEchouees++;
        }
      }

      if (suppressionsEchouees > 0) {
        toast.error(`Suppression partielle : ${suppressionsEchouees} échec(s) sur ${suppressionsReussies + suppressionsEchouees} éléments`);
      } else if (suppressionsReussies > 0) {
        toast.success(`Ligne supprimée complètement (${suppressionsReussies} éléments)`);

        // Nettoyer l'ordre personnalisé si la date supprimée y était
        setCustomOrder(prev => prev.filter(d => d !== date));

        // Forcer le rechargement des hooks
        await loadSnapshots();
        await loadEvolutions();
      } else {
        toast.warning('Aucun élément à supprimer trouvé');
      }

    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error(`Erreur : ${error.message || 'Suppression échouée'}`);
    } finally {
      setIsLoading(false);
    }
  };



  // Obtenir les données combinées pour l'affichage - TOUTES LES LIGNES
  const getCombinedData = (): RowData[] => {
    const evolutionsByDate = getEvolutionsByDate();
    const allDates = new Set([
      ...snapshots.map(s => s.date),
      ...evolutionsByDate.map(e => e.date)
    ]);

    // Créer les lignes existantes avec ordre personnalisé
    let sortedDates = Array.from(allDates);

    if (customOrder.length > 0) {
      // Appliquer l'ordre personnalisé
      const orderedDates = customOrder.filter(date => allDates.has(date));
      const remainingDates = sortedDates.filter(date => !customOrder.includes(date));
      sortedDates = [...orderedDates, ...remainingDates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime())];
    } else {
      // Ordre chronologique par défaut
      sortedDates = sortedDates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    }

    const existingRows = sortedDates.map(date => {
      const snapshot = snapshots.find(s => s.date === date);
      const evolution = evolutionsByDate.find(e => e.date === date);

      return {
        date,
        snapshot,
        evolution,
        fournisseurMontants: evolution?.fournisseurs || {},
        totalCalcule: evolution?.total || 0,
        totalSnapshot: snapshot?.total || 0,
        isEmptyRow: false
      };
    });

    // Ajouter des lignes vides SEULEMENT si demandé
    const emptyRows = [];

    if (showEmptyRows) {
      // Ajouter 2 lignes vides pour permettre l'ajout
      emptyRows.push({
        date: null,
        snapshot: null,
        evolution: null,
        fournisseurMontants: {},
        totalCalcule: 0,
        totalSnapshot: 0,
        isEmptyRow: true,
        emptyRowId: 'empty-0'
      });

      emptyRows.push({
        date: null,
        snapshot: null,
        evolution: null,
        fournisseurMontants: {},
        totalCalcule: 0,
        totalSnapshot: 0,
        isEmptyRow: true,
        emptyRowId: 'empty-1'
      });
    }

    return [...existingRows, ...emptyRows];
  };

  // Fonctions de drag & drop pour réorganiser les lignes
  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (index: number) => {
    if (draggedIndex !== null && draggedIndex !== index) {
      setDragOverIndex(index);
    }
  };

  const handleDragEnd = async () => {
    if (draggedIndex !== null && dragOverIndex !== null && draggedIndex !== dragOverIndex) {
      // Obtenir les données actuelles
      const evolutionsByDate = getEvolutionsByDate();
      const allDates = new Set([
        ...snapshots.map(s => s.date),
        ...evolutionsByDate.map(e => e.date)
      ]);

      const currentData = getCombinedData();
      const draggedItem = currentData[draggedIndex];
      const targetItem = currentData[dragOverIndex];

      // Ne réorganiser que les lignes avec des dates (pas les lignes vides)
      if (draggedItem.date) {
        // Obtenir toutes les dates existantes dans l'ordre actuel
        const existingDates = currentData.filter(item => item.date).map(item => item.date!);

        // Créer le nouvel ordre en déplaçant l'élément
        const newOrder = [...existingDates];
        const draggedDate = newOrder[draggedIndex];

        // Supprimer l'élément de sa position actuelle
        newOrder.splice(draggedIndex, 1);

        // L'insérer à la nouvelle position (en tenant compte des lignes vides)
        let insertIndex = dragOverIndex;
        if (targetItem.date) {
          // Insertion avant une ligne avec données
          const targetDateIndex = newOrder.indexOf(targetItem.date);
          insertIndex = targetDateIndex >= 0 ? targetDateIndex : dragOverIndex;
        } else {
          // Insertion vers une ligne vide - mettre à la fin
          insertIndex = newOrder.length;
        }

        newOrder.splice(insertIndex, 0, draggedDate);

        setCustomOrder(newOrder);
        toast.success(`Ligne déplacée de la position ${draggedIndex + 1} vers ${dragOverIndex + 1}`);
      } else {
        // Déplacement d'une ligne vide
        toast.info('Impossible de déplacer une ligne vide');
      }
    }
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  // Fonction pour supprimer une ligne (vide ou avec données)
  const handleDeleteRow = async (rowData: RowData, index: number) => {
    if (rowData.date) {
      // Ligne avec données réelles - vérifier si c'est un snapshot à 0
      const snapshot = snapshots.find(s => s.date === rowData.date);
      if (snapshot && snapshot.total === 0) {
        // C'est un snapshot vide créé par "Ajouter Ligne" - le supprimer de Supabase
        console.log('🗑️ SUPPRESSION SNAPSHOT VIDE:', snapshot);
        const success = await deleteSnapshot(snapshot.id);
        if (success) {
          toast.success('Ligne vide supprimée de la base de données');
        } else {
          toast.error('Erreur lors de la suppression');
        }
      } else {
        // Ligne avec vraies données - suppression complète
        await handleDeleteEntireLine(rowData.date);
      }
    } else if (rowData.isEmptyRow) {
      // Ligne vide - désactiver l'affichage de toutes les lignes vides
      console.log('🗑️ SUPPRESSION LIGNES VIDES');
      setShowEmptyRows(false);
      toast.success('Lignes vides supprimées - Utilisez "Ajouter Ligne" pour en créer de nouvelles');
    }
  };

  return (
    <div className="p-4 bg-white h-full flex flex-col">
      <div className="flex items-center justify-between mb-3 flex-shrink-0">
        <button
          onClick={() => setShowEvolutionTable(!showEvolutionTable)}
          className="flex items-center gap-3 text-sm font-medium text-blue-700 hover:text-blue-900 transition-colors group"
        >
          <div className="flex items-center gap-2">
            {showEvolutionTable ? (
              <ChevronUp className="h-4 w-4 group-hover:scale-110 transition-transform" />
            ) : (
              <ChevronDown className="h-4 w-4 group-hover:scale-110 transition-transform" />
            )}
            <span className="font-semibold">Évolution Temporelle</span>
          </div>
          {snapshots.length > 0 && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium border border-blue-200">
              {snapshots.length} point{snapshots.length > 1 ? 's' : ''}
            </span>
          )}
        </button>
        
        {showEvolutionTable && (
          <div className="flex gap-2">
            <Button
              onClick={async () => {
                if (isLoading) return;
                setIsLoading(true);
                try {
                  const today = new Date().toISOString().split('T')[0];

                  // Créer le snapshot global avec le total actuel
                  const success = await upsertSnapshot(
                    today,
                    currentTotal,
                    `Point d'évolution créé automatiquement le ${new Date().toLocaleDateString('fr-FR')}`
                  );

                  if (success) {
                    // Créer les entrées détaillées pour chaque fournisseur
                    let fournisseursAjoutes = 0;
                    for (const fournisseur of fournisseurs) {
                      const montantFournisseur = fournisseur.total || 0;
                      if (montantFournisseur > 0) {
                        const successFournisseur = await upsertEvolution(
                          today,
                          fournisseur.id,
                          montantFournisseur,
                          `Montant figé automatiquement`
                        );
                        if (successFournisseur) {
                          fournisseursAjoutes++;
                        }
                      }
                    }



                    toast.success(
                      `Point d'évolution créé avec succès !\n` +
                      `Total global: ${formatMontantEvolution(currentTotal)}\n` +
                      `Détail: ${fournisseursAjoutes} fournisseur${fournisseursAjoutes > 1 ? 's' : ''} enregistré${fournisseursAjoutes > 1 ? 's' : ''}`
                    );
                  }
                } catch (error) {
                  console.error('❌ Erreur lors du figement:', error);
                  toast.error('Erreur lors du figement du total');
                } finally {
                  setIsLoading(false);
                }
              }}
              variant="outline"
              size="sm"
              disabled={isLoading || currentTotal <= 0}
              className="flex items-center gap-2 text-green-700 border-green-200 hover:bg-green-50 hover:border-green-300 transition-all disabled:opacity-50"
              title={currentTotal <= 0 ? 'Aucun montant à figer' : `Figer le total actuel de ${formatMontantEvolution(currentTotal)}`}
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span className="font-medium">
                {isLoading ? 'Figement en cours...' : `Figer ${formatMontantEvolution(currentTotal)}`}
              </span>
            </Button>

            <Button
              onClick={async () => {
                if (isLoading) return;
                setIsLoading(true);
                try {
                  const today = new Date().toISOString().split('T')[0];

                  // Créer un snapshot vide que l'utilisateur peut éditer
                  const success = await upsertSnapshot(today, 0);

                  if (success) {
                    toast.success('Nouvelle ligne vide ajoutée - cliquez pour éditer les montants');
                  }
                } catch (error) {
                  console.error('Erreur lors de l\'ajout:', error);
                  toast.error('Erreur lors de l\'ajout de ligne');
                } finally {
                  setIsLoading(false);
                }
              }}
              variant="outline"
              size="sm"
              disabled={isLoading}
              className="flex items-center gap-2 text-blue-700 border-blue-200 hover:bg-blue-50 hover:border-blue-300 transition-all disabled:opacity-50"
            >
              <Plus className="h-4 w-4" />
              <span className="font-medium">Ajouter Ligne</span>
            </Button>

            {/* Bouton pour réactiver les lignes vides */}
            {!showEmptyRows && (
              <Button
                onClick={() => {
                  console.log('🔄 RÉACTIVATION LIGNES VIDES');
                  setShowEmptyRows(true);
                  toast.success('Lignes vides réactivées');
                }}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 text-gray-600 border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all text-xs"
                title="Réactiver les lignes vides"
              >
                <span className="font-medium">Réactiver lignes vides</span>
              </Button>
            )}

            {/* Bouton pour réinitialiser l'ordre chronologique */}
            {customOrder.length > 0 && (
              <Button
                onClick={() => {
                  setCustomOrder([]);
                  toast.success('Ordre chronologique restauré');
                }}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300 transition-all text-xs"
                title="Remettre l'ordre chronologique"
              >
                <span className="font-medium">Ordre chrono</span>
              </Button>
            )}

            {/* Bouton d'audit complet */}
            <Button
              onClick={lancerAuditComplet}
              variant="outline"
              size="sm"
              className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50 hover:border-purple-300 transition-all text-xs"
              title="Audit complet du projet"
            >
              <span className="font-medium">🔍 Audit</span>
            </Button>
          </div>
        )}
      </div>

      {showEvolutionTable && (
        <div className="mt-3 bg-blue-50/50 border border-blue-200 rounded-lg flex-1 flex flex-col min-h-0">
          <div className="bg-blue-100 px-3 py-2 border-b border-blue-200 flex-shrink-0">
            <h4 className="text-sm font-medium text-blue-800">Points d'évolution du patrimoine par fournisseur</h4>
          </div>

          <div className="p-3 flex-1 overflow-auto min-h-0">
            {/* Indicateur d'erreur */}
            {errorFournisseurs && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
                <div className="text-red-800 text-sm font-medium">Erreur de chargement</div>
                <div className="text-red-600 text-xs mt-1">{errorFournisseurs}</div>
              </div>
            )}

            {/* Indicateur de chargement */}
            {loadingFournisseurs && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                <div className="flex items-center gap-2 text-blue-800 text-sm">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Chargement des données d'évolution...</span>
                </div>
              </div>
            )}

            <div className="border border-blue-200 rounded-lg overflow-auto max-h-96">
              <Table className="min-w-full table-fixed">
                <TableHeader>
                  <TableRow className="bg-blue-50">
                    <TableHead className="text-blue-800 font-medium text-xs w-8 text-center">
                      <GripVertical className="h-4 w-4 mx-auto text-gray-400" />
                    </TableHead>
                    <TableHead className="text-blue-800 font-medium text-xs w-28 sticky left-0 bg-blue-50 z-10 border-r border-blue-200">Date</TableHead>
                    {fournisseurs.map((fournisseur) => (
                      <TableHead key={fournisseur.id} className="text-center text-blue-800 font-medium text-xs w-24 whitespace-nowrap px-1" title={fournisseur.nom}>
                        <div className="truncate">{fournisseur.nom}</div>
                      </TableHead>
                    ))}
                    <TableHead className="text-center text-blue-800 font-medium text-xs w-28">Total</TableHead>
                    <TableHead className="text-center text-blue-800 font-medium text-xs w-16">Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {getCombinedData().map((rowData, index) => (
                  <TableRow
                    key={rowData.date || rowData.emptyRowId || `fallback-${index}`}
                    className={`transition-all duration-200 ${
                      draggedIndex === index
                        ? 'opacity-50 bg-blue-200 shadow-lg transform scale-105'
                        : 'hover:bg-blue-50/50'
                    } ${
                      dragOverIndex === index
                        ? 'border-t-4 border-blue-500 bg-blue-100/50'
                        : ''
                    } ${
                      rowData.date
                        ? 'cursor-grab active:cursor-grabbing'
                        : 'cursor-not-allowed opacity-75'
                    }`}
                    draggable={!!rowData.date}
                    onDragStart={() => rowData.date && handleDragStart(index)}
                    onDragOver={(e) => {
                      e.preventDefault();
                      handleDragOver(index);
                    }}
                    onDragEnd={handleDragEnd}
                  >
                    {/* Drag Handle */}
                    <TableCell className="text-center w-8 px-1">
                      <div className={`flex items-center justify-center transition-all duration-200 ${
                        rowData.date
                          ? 'cursor-grab active:cursor-grabbing hover:bg-blue-100 rounded p-1'
                          : 'cursor-not-allowed opacity-30'
                      }`}>
                        <GripVertical className={`h-4 w-4 transition-colors ${
                          rowData.date
                            ? 'text-gray-500 hover:text-blue-600'
                            : 'text-gray-300'
                        }`} />
                      </div>
                    </TableCell>

                    {/* Date */}
                    <TableCell className="text-xs sticky left-0 bg-white z-10 border-r border-gray-200">
                      {editingCell && editingCell.field === 'date' && editingCell.index === index ? (
                        <Input
                          type="date"
                          value={editingCell.value}
                          onChange={(e) => setEditingCell({ ...editingCell, value: e.target.value })}
                          className="w-full text-xs border-blue-300 focus:border-blue-500"
                          autoFocus
                          onBlur={handleSaveEdit}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleSaveEdit();
                            if (e.key === 'Escape') setEditingCell(null);
                          }}
                        />
                      ) : (
                        <div
                          className={`cursor-pointer hover:bg-blue-100 px-2 py-1 rounded font-medium transition-all duration-200 whitespace-nowrap ${
                            rowData.date ? 'text-blue-700' : 'text-gray-400 border border-dashed border-gray-300 hover:border-blue-300'
                          } ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
                          onClick={() => {
                            if (isLoading) return;
                            const dateValue = rowData.date || new Date().toISOString().split('T')[0];
                            setEditingCell({
                              index,
                              date: dateValue,
                              field: 'date',
                              value: dateValue
                            });
                          }}
                          title={rowData.date ? "Cliquez pour modifier la date" : "Cliquez pour ajouter une date"}
                        >
                          {rowData.date ? new Date(rowData.date).toLocaleDateString('fr-FR') : '+ Ajouter date'}
                        </div>
                      )}
                    </TableCell>

                    {/* Colonnes fournisseurs */}
                    {fournisseurs.map((fournisseur) => {
                      const montant = rowData.fournisseurMontants[fournisseur.id] || 0;
                      const isEditing = editingCell &&
                        editingCell.field === 'fournisseur' &&
                        editingCell.fournisseurId === fournisseur.id &&
                        editingCell.index === index;

                      return (
                        <TableCell key={fournisseur.id} className="text-center w-24 px-1">
                          {isEditing ? (
                            <Input
                              type="number"
                              value={editingCell.value}
                              onChange={(e) => setEditingCell({ ...editingCell, value: e.target.value })}
                              className="w-full text-xs border-blue-300 focus:border-blue-500"
                              autoFocus
                              onBlur={handleSaveEdit}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') handleSaveEdit();
                                if (e.key === 'Escape') setEditingCell(null);
                              }}
                            />
                          ) : (
                            <div
                              className={`text-xs cursor-pointer hover:bg-blue-100 px-1 py-1 rounded transition-all duration-200 ${
                                montant > 0 ? 'text-green-700 font-medium bg-green-50 border border-green-200' :
                                rowData.date ? 'text-gray-400 hover:text-blue-600' : 'text-gray-300 border border-dashed border-gray-200'
                              } ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
                              onDoubleClick={() => {
                                if (isLoading) return;
                                if (!rowData.date) {
                                  toast.error('Veuillez d\'abord définir une date');
                                  return;
                                }
                                setEditingCell({
                                  index,
                                  date: rowData.date,
                                  field: 'fournisseur',
                                  fournisseurId: fournisseur.id,
                                  value: montant.toString()
                                });
                              }}
                              title={rowData.date ? `Double-cliquez pour éditer le montant ${fournisseur.nom}` : "Définissez d'abord une date"}
                            >
                              <div className="truncate">
                                {montant > 0 ? formatMontantFournisseur(montant).replace(' €', '€') :
                                 rowData.date ? '••' : '---'}
                              </div>
                            </div>
                          )}
                        </TableCell>
                      );
                    })}

                    {/* Total */}
                    <TableCell className="text-center">
                      {editingCell && editingCell.field === 'total' && editingCell.index === index ? (
                        <Input
                          type="number"
                          value={editingCell.value}
                          onChange={(e) => setEditingCell({ ...editingCell, value: e.target.value })}
                          className="w-full text-xs border-blue-300 focus:border-blue-500"
                          autoFocus
                          onBlur={handleSaveEdit}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleSaveEdit();
                            if (e.key === 'Escape') setEditingCell(null);
                          }}
                        />
                      ) : (
                        <div
                          className={`text-xs font-medium cursor-pointer hover:bg-blue-100 px-2 py-1 rounded transition-all duration-200 ${
                            rowData.totalSnapshot > 0 ? 'text-green-700 bg-green-50 border border-green-200' :
                            rowData.totalCalcule > 0 ? 'text-blue-700 bg-blue-50 border border-blue-200' :
                            rowData.date ? 'text-gray-400 hover:text-blue-600' : 'text-gray-300 border border-dashed border-gray-200'
                          } ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
                          onDoubleClick={() => {
                            if (isLoading) return;
                            if (!rowData.date) {
                              toast.error('Veuillez d\'abord définir une date');
                              return;
                            }
                            setEditingCell({
                              index,
                              date: rowData.date,
                              field: 'total',
                              value: (rowData.totalSnapshot || rowData.totalCalcule || 0).toString()
                            });
                          }}
                          title={rowData.date ? "Double-cliquez pour éditer le total global" : "Définissez d'abord une date"}
                        >
                          {rowData.totalSnapshot > 0
                            ? formatMontantEvolution(rowData.totalSnapshot)
                            : rowData.totalCalcule > 0
                            ? formatMontantFournisseur(rowData.totalCalcule)
                            : rowData.date ? 'Double-clic' : '---'
                          }
                        </div>
                      )}
                    </TableCell>

                    {/* Actions */}
                    <TableCell className="text-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteRow(rowData, index)}
                        disabled={isLoading}
                        className={`h-6 w-6 p-0 ${
                          rowData.date
                            ? 'text-red-500 hover:bg-red-50'
                            : 'text-gray-400 hover:bg-gray-50 hover:text-red-400'
                        }`}
                        title={rowData.date ? "Supprimer toute la ligne" : "Supprimer la ligne vide"}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            </div>

            {/* Message d'aide */}
            {getCombinedData().length === 0 && !loadingFournisseurs && (
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm mb-2">Aucun point d'évolution défini</div>
                <div className="text-xs">
                  Utilisez le bouton "Figer Total Actuel" pour commencer à suivre l'évolution
                </div>
              </div>
            )}

            {/* Indicateur d'état des lignes vides */}
            {!showEmptyRows && (
              <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-lg">
                <div className="text-xs text-green-800">
                  <strong>✅ Mode épuré :</strong> Lignes vides désactivées - Interface propre
                </div>
              </div>
            )}

            {/* Légende */}
            {getCombinedData().length > 0 && (
              <div className="mt-4 pt-3 border-t border-blue-200 bg-blue-50/30 rounded-lg p-2">
                <div className="text-xs text-blue-600 space-y-1">
                  <div className="font-medium mb-2">Guide d'utilisation :</div>
                  <div>• <strong>Clic sur date</strong> : Modifier la date</div>
                  <div>• <strong>Double-clic sur fournisseur</strong> : Éditer le montant</div>
                  <div>• <strong>Double-clic sur total</strong> : Éditer le total global</div>
                  <div>• <strong>Total vert</strong> : Saisi manuellement</div>
                  <div>• <strong>Total bleu</strong> : Calculé automatiquement</div>
                  <div>• <strong>🗑️ Icône poubelle</strong> : Supprimer la ligne (vide ou avec données)</div>
                </div>
              </div>
            )}


          </div>
        </div>
      )}
    </div>
  );
};
