-- Migration pour créer les tables d'évolution du patrimoine
-- Ces tables sont nécessaires pour le suivi temporel de l'évolution du patrimoine

-- 1. Créer la table patrimoine_evolution pour les snapshots globaux
CREATE TABLE IF NOT EXISTS public.patrimoine_evolution (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date_snapshot DATE NOT NULL,
  total_sous_gestion TEXT NOT NULL, -- Chiffré avec AES-256-GCM
  devise TEXT NOT NULL DEFAULT 'EUR',
  commentaire TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Contrainte d'unicité sur la date
  CONSTRAINT unique_date_snapshot UNIQUE (date_snapshot)
);

-- 2. Créer la table patrimoine_evolution_fournisseurs pour les détails par fournisseur
CREATE TABLE IF NOT EXISTS public.patrimoine_evolution_fournisseurs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date_snapshot DATE NOT NULL,
  fournisseur_id UUID NOT NULL REFERENCES public.patrimoine_fournisseurs(id) ON DELETE CASCADE,
  montant_chiffre TEXT NOT NULL, -- Chiffré avec AES-256-GCM
  devise TEXT NOT NULL DEFAULT 'EUR',
  commentaire TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Contrainte d'unicité sur date + fournisseur
  CONSTRAINT unique_date_fournisseur UNIQUE (date_snapshot, fournisseur_id)
);

-- 3. Créer les index pour les performances
CREATE INDEX IF NOT EXISTS idx_patrimoine_evolution_date ON public.patrimoine_evolution(date_snapshot);
CREATE INDEX IF NOT EXISTS idx_patrimoine_evolution_fournisseurs_date ON public.patrimoine_evolution_fournisseurs(date_snapshot);
CREATE INDEX IF NOT EXISTS idx_patrimoine_evolution_fournisseurs_fournisseur ON public.patrimoine_evolution_fournisseurs(fournisseur_id);

-- 4. Créer les triggers pour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_patrimoine_evolution_updated_at 
  BEFORE UPDATE ON public.patrimoine_evolution 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_patrimoine_evolution_fournisseurs_updated_at 
  BEFORE UPDATE ON public.patrimoine_evolution_fournisseurs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 5. Activer RLS (Row Level Security)
ALTER TABLE public.patrimoine_evolution ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.patrimoine_evolution_fournisseurs ENABLE ROW LEVEL SECURITY;

-- 6. Créer les politiques RLS (accès complet pour les utilisateurs authentifiés)
CREATE POLICY "Utilisateurs authentifiés peuvent tout faire sur patrimoine_evolution" 
  ON public.patrimoine_evolution 
  FOR ALL 
  TO authenticated 
  USING (true) 
  WITH CHECK (true);

CREATE POLICY "Utilisateurs authentifiés peuvent tout faire sur patrimoine_evolution_fournisseurs" 
  ON public.patrimoine_evolution_fournisseurs 
  FOR ALL 
  TO authenticated 
  USING (true) 
  WITH CHECK (true);

-- 7. Activer les souscriptions temps réel
ALTER PUBLICATION supabase_realtime ADD TABLE public.patrimoine_evolution;
ALTER PUBLICATION supabase_realtime ADD TABLE public.patrimoine_evolution_fournisseurs;

-- 8. Ajouter des commentaires sur les tables
COMMENT ON TABLE public.patrimoine_evolution IS 'Table des snapshots globaux d''évolution du patrimoine avec données chiffrées';
COMMENT ON COLUMN public.patrimoine_evolution.total_sous_gestion IS 'Total sous gestion chiffré avec AES-256-GCM (format: enc:base64data)';
COMMENT ON TABLE public.patrimoine_evolution_fournisseurs IS 'Table des détails d''évolution par fournisseur avec données chiffrées';
COMMENT ON COLUMN public.patrimoine_evolution_fournisseurs.montant_chiffre IS 'Montant chiffré avec AES-256-GCM (format: enc:base64data)';

-- 9. Journaliser la création des tables
INSERT INTO public.data_access_logs (
  operation,
  table_name,
  details,
  timestamp
) VALUES (
  'create',
  'system',
  'Création des tables d''évolution du patrimoine (patrimoine_evolution, patrimoine_evolution_fournisseurs)',
  NOW()
);

-- 10. Vérifier que les tables ont été créées
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'patrimoine_evolution') AND
     EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'patrimoine_evolution_fournisseurs') THEN
    RAISE NOTICE '✅ Tables d''évolution du patrimoine créées avec succès';
  ELSE
    RAISE WARNING '❌ Erreur lors de la création des tables d''évolution';
  END IF;
END $$;
