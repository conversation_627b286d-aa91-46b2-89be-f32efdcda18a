/**
 * Composant de monitoring des performances
 * Mesure les temps de chargement et affiche les métriques
 */

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Zap, Database, TrendingUp } from 'lucide-react';

interface PerformanceMetrics {
  initialLoad: number;
  cacheHits: number;
  cacheMisses: number;
  averageQueryTime: number;
  totalQueries: number;
  lastUpdate: Date;
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    initialLoad: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageQueryTime: 0,
    totalQueries: 0,
    lastUpdate: new Date()
  });

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Mesurer le temps de chargement initial
    const startTime = performance.now();
    
    // Simuler la fin du chargement initial
    const timer = setTimeout(() => {
      const loadTime = performance.now() - startTime;
      setMetrics(prev => ({
        ...prev,
        initialLoad: loadTime,
        lastUpdate: new Date()
      }));
    }, 100);

    // Écouter les événements de performance personnalisés
    const handlePerformanceEvent = (event: CustomEvent) => {
      const { type, duration, cached } = event.detail;
      
      setMetrics(prev => ({
        ...prev,
        totalQueries: prev.totalQueries + 1,
        cacheHits: cached ? prev.cacheHits + 1 : prev.cacheHits,
        cacheMisses: !cached ? prev.cacheMisses + 1 : prev.cacheMisses,
        averageQueryTime: (prev.averageQueryTime * (prev.totalQueries - 1) + duration) / prev.totalQueries,
        lastUpdate: new Date()
      }));
    };

    window.addEventListener('patrimoine-performance', handlePerformanceEvent as EventListener);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('patrimoine-performance', handlePerformanceEvent as EventListener);
    };
  }, []);

  // Calculer le taux de cache hit
  const cacheHitRate = metrics.totalQueries > 0 
    ? (metrics.cacheHits / metrics.totalQueries * 100).toFixed(1)
    : '0';

  // Déterminer la couleur selon les performances
  const getPerformanceColor = (time: number) => {
    if (time < 500) return 'text-green-600';
    if (time < 1000) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (time: number) => {
    if (time < 500) return 'default';
    if (time < 1000) return 'secondary';
    return 'destructive';
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
          title="Afficher les métriques de performance"
        >
          <Zap className="h-4 w-4" />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80 shadow-xl border-0 bg-white/95 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Zap className="h-4 w-4 text-blue-600" />
              Performance
            </CardTitle>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-gray-600 text-xs"
            >
              ✕
            </button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Temps de chargement initial */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3 text-gray-500" />
              <span className="text-xs text-gray-600">Chargement initial</span>
            </div>
            <Badge variant={getPerformanceBadge(metrics.initialLoad)} className="text-xs">
              {metrics.initialLoad.toFixed(0)}ms
            </Badge>
          </div>

          {/* Temps de requête moyen */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-3 w-3 text-gray-500" />
              <span className="text-xs text-gray-600">Requête moyenne</span>
            </div>
            <Badge variant={getPerformanceBadge(metrics.averageQueryTime)} className="text-xs">
              {metrics.averageQueryTime.toFixed(0)}ms
            </Badge>
          </div>

          {/* Taux de cache hit */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-3 w-3 text-gray-500" />
              <span className="text-xs text-gray-600">Cache hit rate</span>
            </div>
            <Badge 
              variant={parseFloat(cacheHitRate) > 80 ? 'default' : 'secondary'} 
              className="text-xs"
            >
              {cacheHitRate}%
            </Badge>
          </div>

          {/* Statistiques détaillées */}
          <div className="pt-2 border-t border-gray-200">
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="text-center">
                <div className="font-medium text-green-600">{metrics.cacheHits}</div>
                <div className="text-gray-500">Cache hits</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-red-600">{metrics.cacheMisses}</div>
                <div className="text-gray-500">Cache miss</div>
              </div>
            </div>
          </div>

          {/* Dernière mise à jour */}
          <div className="text-xs text-gray-400 text-center">
            Mis à jour: {metrics.lastUpdate.toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Fonction utilitaire pour émettre des événements de performance
export const emitPerformanceEvent = (type: string, duration: number, cached: boolean = false) => {
  const event = new CustomEvent('patrimoine-performance', {
    detail: { type, duration, cached }
  });
  window.dispatchEvent(event);
};
