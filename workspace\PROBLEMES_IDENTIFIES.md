# 🔍 **PROBLÈMES IDENTIFIÉS ET SOLUTIONS**

## ❌ **PROBLÈME 1 : LIGNES VIDES QUI RÉAPPARAISSENT**

### **Cause Racine**
```typescript
// LIGNE 288 - PatrimoineEvolutionToggle.tsx
for (let i = 0; i < 2; i++) {
  const emptyRowId = `empty-${i}`;
  
  // Ajouter SEULEMENT si la ligne n'est PAS cachée
  if (!hiddenEmptyRows.has(emptyRowId)) {
    emptyRows.push({...}); // ← PROBLÈME : Génère toujours empty-0 et empty-1
  }
}
```

### **Problème**
- La boucle génère TOUJOURS `empty-0` et `empty-1`
- Si `empty-0` est caché, la boucle génère quand même `empty-1`
- Si `empty-1` est caché, la boucle génère quand même `empty-0`
- **Résultat** : Il y a TOUJOURS au moins une ligne vide visible

### **✅ Solution**
```typescript
// SOLUTION CORRECTE
const emptyRows = [];

// Générer SEULEMENT les lignes NON cachées
if (!hiddenEmptyRows.has('empty-0')) {
  emptyRows.push({...emptyRowId: 'empty-0'});
}
if (!hiddenEmptyRows.has('empty-1')) {
  emptyRows.push({...emptyRowId: 'empty-1'});
}

// Résultat : Si les deux sont cachées, emptyRows = [] (aucune ligne vide)
```

## ❌ **PROBLÈME 2 : COURBES N'AFFICHENT PAS TOUS LES POINTS**

### **Cause Racine**
```typescript
// usePatrimoineEvolution.ts - LIGNE 70
const chartData = useMemo((): ChartDataPoint[] => {
  if (selectedFournisseurs.length === 0) {
    return prepareGlobalChartData(snapshots); // ← PROBLÈME : Utilise SEULEMENT snapshots
  }
  return prepareFournisseursChartData(snapshots, selectedFournisseurs, patrimoineData);
}, [snapshots, selectedFournisseurs, patrimoineData]);
```

### **Problème**
- **Tableau d'évolution** affiche : `snapshots` + `evolutionsByDate` (2 sources)
- **Courbes** affichent : SEULEMENT `snapshots` (1 source)
- **Résultat** : Points ajoutés dans le tableau ne s'affichent pas dans les courbes

### **Sources de Données**
1. **`snapshots`** : Table `patrimoine_evolution` (totaux globaux)
2. **`evolutionsByDate`** : Table `patrimoine_evolution_fournisseurs` (détails par fournisseur)

### **✅ Solution**
```typescript
// CORRIGER usePatrimoineEvolution.ts
import { usePatrimoineEvolutionFournisseurs } from './usePatrimoineEvolutionFournisseurs';

const { getEvolutionsByDate } = usePatrimoineEvolutionFournisseurs();

const chartData = useMemo((): ChartDataPoint[] => {
  // COMBINER les deux sources de données
  const evolutionsByDate = getEvolutionsByDate();
  const allDates = new Set([
    ...snapshots.map(s => s.date),
    ...evolutionsByDate.map(e => e.date)
  ]);

  return Array.from(allDates).map(date => {
    const snapshot = snapshots.find(s => s.date === date);
    const evolution = evolutionsByDate.find(e => e.date === date);
    
    return {
      date,
      dateFormatted: formatDateForChart(date),
      total: snapshot?.total || evolution?.total || 0,
      // Ajouter les données par fournisseur si nécessaire
      ...evolution?.fournisseurs || {}
    };
  });
}, [snapshots, getEvolutionsByDate]);
```

## 🎯 **PLAN DE CORRECTION**

### **Étape 1 : Corriger les Lignes Vides**
1. Modifier la logique de génération dans `getCombinedData()`
2. Permettre 0 ligne vide si toutes sont supprimées
3. Tester la suppression définitive

### **Étape 2 : Corriger les Courbes**
1. Intégrer `usePatrimoineEvolutionFournisseurs` dans `usePatrimoineEvolution`
2. Combiner `snapshots` + `evolutionsByDate` pour les données de graphique
3. S'assurer que tous les points du tableau apparaissent dans les courbes

### **Étape 3 : Tests de Validation**
1. **Test lignes vides** : Supprimer toutes les lignes → Aucune ligne visible
2. **Test courbes** : Ajouter point dans tableau → Point visible dans courbe
3. **Test synchronisation** : Modification tableau → Mise à jour courbe temps réel

## 🚨 **PRIORITÉ CRITIQUE**

Ces deux problèmes empêchent l'utilisation normale de l'application :
1. **Lignes vides** : Frustration utilisateur, interface encombrée
2. **Courbes incomplètes** : Données manquantes, analyse impossible

**Action immédiate requise pour corriger ces problèmes fondamentaux.**
