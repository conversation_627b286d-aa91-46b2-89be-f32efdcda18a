/**
 * Composant de statut temps réel pour vérifier la synchronisation
 * Affiche l'état des connexions temps réel
 */

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, Clock } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

export const RealtimeStatus: React.FC = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [connectionCount, setConnectionCount] = useState(0);

  useEffect(() => {
    // Vérifier l'état de la connexion
    const checkConnection = () => {
      const channels = supabase.getChannels();
      const activeChannels = channels.filter(channel => 
        channel.state === 'joined' || channel.state === 'joining'
      );
      
      setIsConnected(activeChannels.length > 0);
      setConnectionCount(activeChannels.length);
    };

    // Vérifier immédiatement
    checkConnection();

    // Vérifier périodiquement
    const interval = setInterval(checkConnection, 2000);

    // Écouter les changements de statut
    const subscription = supabase
      .channel('realtime-status-test')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'client_patrimoine' },
        () => {
          setLastUpdate(new Date());
        }
      )
      .subscribe();

    return () => {
      clearInterval(interval);
      subscription.unsubscribe();
    };
  }, []);

  if (!isConnected) {
    return (
      <Badge variant="destructive" className="text-xs">
        <WifiOff className="h-3 w-3 mr-1" />
        Hors ligne
      </Badge>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Badge variant="default" className="text-xs bg-green-100 text-green-800">
        <Wifi className="h-3 w-3 mr-1" />
        Temps réel ({connectionCount})
      </Badge>
      {lastUpdate && (
        <Badge variant="outline" className="text-xs">
          <Clock className="h-3 w-3 mr-1" />
          {lastUpdate.toLocaleTimeString()}
        </Badge>
      )}
    </div>
  );
};
