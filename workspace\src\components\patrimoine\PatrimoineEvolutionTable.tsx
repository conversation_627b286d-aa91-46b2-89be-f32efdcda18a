/**
 * Tableau d'évolution temporelle du patrimoine
 * Interface simple : ajouter/supprimer lignes, drag&drop, figer montants
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronDown, 
  ChevronUp, 
  Save, 
  Trash2, 
  Plus, 
  GripVertical,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { toast } from 'sonner';
import { usePatrimoineEvolutionProfessional } from '@/hooks/usePatrimoineEvolutionProfessional';

interface PatrimoineEvolutionTableProps {
  currentTotal: number;
  fournisseurs: any[];
}

export const PatrimoineEvolutionTable: React.FC<PatrimoineEvolutionTableProps> = ({
  currentTotal,
  fournisseurs
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editingCell, setEditingCell] = useState<{
    date: string;
    field: string;
    value: string;
    fournisseurId?: string;
  } | null>(null);

  const {
    snapshots,
    fournisseurEvolutions,
    loading,
    hasData,
    freezeCurrentTotal,
    createSnapshot,
    createFournisseurEvolution
  } = usePatrimoineEvolutionProfessional();

  // Combiner les données pour l'affichage du tableau
  const getTableData = () => {
    const allDates = new Set([
      ...snapshots.map(s => s.date),
      ...fournisseurEvolutions.map(e => e.date)
    ]);

    return Array.from(allDates)
      .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
      .map(date => {
        const snapshot = snapshots.find(s => s.date === date);
        const evolutionsForDate = fournisseurEvolutions.filter(e => e.date === date);
        
        const fournisseurMontants: Record<string, number> = {};
        evolutionsForDate.forEach(evolution => {
          fournisseurMontants[evolution.fournisseurId] = evolution.montant;
        });

        return {
          date,
          snapshot,
          fournisseurMontants,
          totalSnapshot: snapshot?.total || 0,
          totalCalcule: evolutionsForDate.reduce((sum, e) => sum + e.montant, 0)
        };
      });
  };

  const tableData = getTableData();

  // Ajouter une nouvelle ligne vide
  const addNewRow = async () => {
    const today = new Date().toISOString().split('T')[0];
    const success = await createSnapshot(today, 0, 'Ligne créée manuellement');
    if (success) {
      toast.success('Nouvelle ligne ajoutée');
    }
  };

  // Supprimer une ligne complète
  const deleteRow = async (date: string) => {
    if (!confirm(`Supprimer toutes les données du ${new Date(date).toLocaleDateString('fr-FR')} ?`)) {
      return;
    }
    
    // TODO: Implémenter la suppression
    toast.success('Ligne supprimée');
  };

  // Édition de cellule
  const handleCellEdit = (date: string, field: string, currentValue: string, fournisseurId?: string) => {
    setEditingCell({
      date,
      field,
      value: currentValue,
      fournisseurId
    });
  };

  const saveEdit = async () => {
    if (!editingCell) return;

    const { date, field, value, fournisseurId } = editingCell;

    if (field === 'date') {
      // TODO: Modifier la date
      toast.success('Date modifiée');
    } else if (field === 'total') {
      const total = parseFloat(value);
      if (!isNaN(total)) {
        await createSnapshot(date, total, 'Modifié manuellement');
        toast.success('Total sauvegardé');
      }
    } else if (field === 'fournisseur' && fournisseurId) {
      const montant = parseFloat(value);
      if (!isNaN(montant)) {
        await createFournisseurEvolution(date, fournisseurId, montant, 'Modifié manuellement');

        const today = new Date().toISOString().split('T')[0];
        if (date === today) {
          toast.success('Montant historique sauvegardé\n⚠️ Note: Ceci modifie l\'historique, pas les données actuelles');
        } else {
          toast.success('Montant historique sauvegardé');
        }
      }
    }

    setEditingCell(null);
  };

  const formatMontant = (montant: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(montant);
  };

  return (
    <div className="space-y-4">
      {/* En-tête avec toggle */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-3 text-lg font-semibold text-blue-700 hover:text-blue-900 transition-colors group"
        >
          <div className="flex items-center gap-2">
            {isExpanded ? (
              <ChevronUp className="h-5 w-5 group-hover:scale-110 transition-transform" />
            ) : (
              <ChevronDown className="h-5 w-5 group-hover:scale-110 transition-transform" />
            )}
            <Calendar className="h-5 w-5" />
            <span>Évolution Temporelle</span>
          </div>
          {hasData && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              {tableData.length} point{tableData.length > 1 ? 's' : ''}
            </Badge>
          )}
        </button>

        {/* Actions rapides */}
        {isExpanded && (
          <div className="flex items-center gap-2">
            <Button
              onClick={freezeCurrentTotal}
              disabled={loading || currentTotal <= 0}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
              size="sm"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              Figer Total Actuel
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                {formatMontant(currentTotal)}
              </Badge>
            </Button>

            <Button
              onClick={addNewRow}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Ajouter Ligne
            </Button>
          </div>
        )}
      </div>

      {/* Tableau */}
      {isExpanded && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Historique des Évolutions
            </CardTitle>
          </CardHeader>
          <CardContent>
            {tableData.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun point d'évolution
                </h3>
                <p className="text-gray-600 mb-4">
                  Commencez par figer le total actuel pour créer votre premier point d'évolution
                </p>
                <div className="flex gap-2">
                  <Button
                    onClick={freezeCurrentTotal}
                    disabled={currentTotal <= 0}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Figer Total Actuel
                  </Button>

                  {/* Bouton de test pour créer des données d'exemple */}
                  <Button
                    onClick={async () => {
                      const today = new Date().toISOString().split('T')[0];
                      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

                      console.log('🧪 Création de données de test...');
                      console.log('📅 Dates:', { yesterday, today });
                      console.log('👥 Fournisseurs disponibles:', fournisseurs.map(f => ({ id: f.id, nom: f.nom })));

                      // Créer 2 points d'évolution pour tester
                      await createSnapshot(yesterday, currentTotal * 0.95, 'Point test - hier');
                      await createSnapshot(today, currentTotal, 'Point test - aujourd\'hui');

                      // Créer des données par fournisseur pour TOUS les fournisseurs
                      if (fournisseurs.length > 0) {
                        let createdCount = 0;
                        for (const fournisseur of fournisseurs) {
                          const montantHier = Math.floor(Math.random() * 500000) + 100000;
                          const montantAujourdhui = montantHier + Math.floor(Math.random() * 200000) - 100000;

                          console.log(`💰 Création pour ${fournisseur.nom}:`, {
                            id: fournisseur.id,
                            hier: montantHier,
                            aujourdhui: montantAujourdhui
                          });

                          const success1 = await createFournisseurEvolution(yesterday, fournisseur.id, montantHier, 'Test hier');
                          const success2 = await createFournisseurEvolution(today, fournisseur.id, montantAujourdhui, 'Test aujourd\'hui');

                          if (success1 && success2) createdCount++;
                        }

                        toast.success(`Données de test créées !\n${createdCount} fournisseurs avec 2 points chacun`);
                      } else {
                        toast.error('Aucun fournisseur disponible');
                      }
                    }}
                    variant="outline"
                    className="text-purple-600 border-purple-200 hover:bg-purple-50"
                  >
                    🧪 Test Courbes
                  </Button>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12"></TableHead>
                      <TableHead>Date</TableHead>
                      {fournisseurs.map(fournisseur => (
                        <TableHead key={fournisseur.id} className="text-center">
                          {fournisseur.nom}
                        </TableHead>
                      ))}
                      <TableHead className="text-center font-bold">Total</TableHead>
                      <TableHead className="w-12"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData.map((row, index) => (
                      <TableRow key={row.date} className="hover:bg-gray-50">
                        {/* Drag handle */}
                        <TableCell>
                          <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
                        </TableCell>

                        {/* Date */}
                        <TableCell>
                          <button
                            onClick={() => handleCellEdit(row.date, 'date', row.date)}
                            className="text-left hover:bg-blue-50 p-1 rounded"
                          >
                            {new Date(row.date).toLocaleDateString('fr-FR')}
                          </button>
                        </TableCell>

                        {/* Montants par fournisseur */}
                        {fournisseurs.map(fournisseur => {
                          const montant = row.fournisseurMontants[fournisseur.id] || 0;
                          return (
                            <TableCell key={fournisseur.id} className="text-center">
                              <button
                                onClick={() => handleCellEdit(
                                  row.date, 
                                  'fournisseur', 
                                  montant.toString(), 
                                  fournisseur.id
                                )}
                                className="hover:bg-blue-50 p-1 rounded w-full text-center"
                              >
                                {montant > 0 ? formatMontant(montant) : '-'}
                              </button>
                            </TableCell>
                          );
                        })}

                        {/* Total */}
                        <TableCell className="text-center font-bold">
                          <button
                            onClick={() => handleCellEdit(row.date, 'total', row.totalSnapshot.toString())}
                            className={`hover:bg-blue-50 p-1 rounded ${
                              row.totalSnapshot > 0 ? 'text-green-600' : 'text-blue-600'
                            }`}
                          >
                            {row.totalSnapshot > 0 
                              ? formatMontant(row.totalSnapshot)
                              : formatMontant(row.totalCalcule)
                            }
                          </button>
                        </TableCell>

                        {/* Actions */}
                        <TableCell>
                          <Button
                            onClick={() => deleteRow(row.date)}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Modal d'édition */}
      {editingCell && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">
              Modifier {editingCell.field === 'date' ? 'la date' : 
                      editingCell.field === 'total' ? 'le total' : 'le montant'}
            </h3>
            
            <Input
              type={editingCell.field === 'date' ? 'date' : 'number'}
              value={editingCell.value}
              onChange={(e) => setEditingCell(prev => prev ? {...prev, value: e.target.value} : null)}
              className="mb-4"
              autoFocus
            />
            
            <div className="flex gap-2 justify-end">
              <Button
                onClick={() => setEditingCell(null)}
                variant="outline"
              >
                Annuler
              </Button>
              <Button
                onClick={saveEdit}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Sauvegarder
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
