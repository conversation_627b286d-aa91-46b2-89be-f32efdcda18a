/**
 * Composant d'évolution compact pour les analytics
 * Version simplifiée avec bouton plein écran
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { TrendingUp, Maximize2, Calendar } from 'lucide-react';
import { usePatrimoineEvolutionProfessional } from '@/hooks/usePatrimoineEvolutionProfessional';
import { FournisseurSelector } from './FournisseurSelector';
import { formatMontant, formatMontantForAxis, formatDateForChart } from '@/utils/patrimoine-formatters';

interface EvolutionChartCompactProps {
  className?: string;
}

export const EvolutionChartCompact: React.FC<EvolutionChartCompactProps> = ({ className }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const {
    loading,
    hasData,
    fournisseursDisponibles,
    chartData,
    metrics,
    selectedFournisseurs,
    showTotalCurve,
    toggleFournisseur,
    selectAllFournisseurs,
    clearSelection,
    toggleTotalCurve,
    loadAllData
  } = usePatrimoineEvolutionProfessional();

  // Le chargement est géré par le hook lui-même

  // Log temporaire pour debug
  useEffect(() => {
    if (chartData.length > 0) {
      console.log('🔍 Premier élément chartData:', chartData[0]);
      console.log('🔍 Tous les éléments chartData:', chartData);

      const availableKeys = Object.keys(chartData[0]).filter(key => key !== 'date' && key !== 'dateFormatted');
      const visibleKeys = availableKeys.filter(key => {
        if (key === 'total') return showTotalCurve;
        return selectedFournisseurs.includes(key);
      });

      console.log('🎯 Courbes disponibles:', availableKeys);
      console.log('🎯 Fournisseurs sélectionnés:', selectedFournisseurs);
      console.log('🎯 Courbes visibles:', visibleKeys);
      console.log('🎯 Afficher total:', showTotalCurve);
    }
  }, [chartData, selectedFournisseurs, showTotalCurve]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white/95 backdrop-blur-xl p-3 border-0 rounded-xl shadow-xl">
          <div className="flex items-center gap-2 mb-2">
            <span className="font-medium text-gray-800 text-sm">{label}</span>
          </div>
          <div className="space-y-1">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: entry.color }}
                ></div>
                <span className="text-xs text-gray-600">{entry.name}:</span>
                <span className="text-sm font-bold" style={{ color: entry.color }}>
                  {formatMontant(entry.value)}
                </span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-72 flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-72 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <Calendar className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p className="text-sm">Aucune donnée d'évolution</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Évolution du Patrimoine
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFullscreen(true)}
              className="border-blue-200 text-blue-600 hover:bg-blue-50"
            >
              <Maximize2 className="h-4 w-4 mr-1" />
              Plein écran
            </Button>
          </div>

          {/* Sélecteur de fournisseurs compact */}
          <div className="mt-3">
            <FournisseurSelector
              fournisseurs={fournisseursDisponibles}
              selectedFournisseurs={selectedFournisseurs}
              showTotalCurve={showTotalCurve}
              onFournisseurToggle={toggleFournisseur}
              onSelectAll={selectAllFournisseurs}
              onClearSelection={clearSelection}
              onToggleTotalCurve={toggleTotalCurve}
              compact={true}
            />
          </div>
        </CardHeader>
        <CardContent className="flex-grow p-6">
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{ top: 10, right: 10, left: 10, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" opacity={0.5} />
                <XAxis
                  dataKey="dateFormatted"
                  stroke="#9CA3AF"
                  fontSize={10}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#9CA3AF"
                  fontSize={10}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={formatMontantForAxis}
                />
                <Tooltip content={<CustomTooltip />} />

                {/* Rendu des lignes dynamiques selon la sélection */}
                {chartData.length > 0 && Object.keys(chartData[0])
                  .filter(key => {
                    if (key === 'date' || key === 'dateFormatted') return false;
                    if (key === 'total') return showTotalCurve;
                    return selectedFournisseurs.includes(key);
                  })
                  .map((key, index) => {
                    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316', '#84CC16'];
                    return (
                      <Line
                        key={key}
                        type="monotone"
                        dataKey={key}
                        stroke={colors[index % colors.length]}
                        strokeWidth={key === 'total' ? 3 : 2}
                        dot={{ r: key === 'total' ? 4 : 3 }}
                        activeDot={{ r: key === 'total' ? 6 : 5 }}
                        name={key === 'total' ? 'Total' : key}
                      />
                    );
                  })}
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Dialog plein écran */}
      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogContent className="max-w-6xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Évolution du Patrimoine - Vue Détaillée
            </DialogTitle>
          </DialogHeader>

          {/* Sélecteur de fournisseurs complet */}
          <div className="mb-4">
            <FournisseurSelector
              fournisseurs={fournisseursDisponibles}
              selectedFournisseurs={selectedFournisseurs}
              showTotalCurve={showTotalCurve}
              onFournisseurToggle={toggleFournisseur}
              onSelectAll={selectAllFournisseurs}
              onClearSelection={clearSelection}
              onToggleTotalCurve={toggleTotalCurve}
              compact={false}
            />
          </div>

          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 30, bottom: 20 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" opacity={0.6} />
                <XAxis
                  dataKey="dateFormatted"
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={formatMontantForAxis}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />

                {/* Rendu des lignes dynamiques selon la sélection */}
                {chartData.length > 0 && Object.keys(chartData[0])
                  .filter(key => {
                    if (key === 'date' || key === 'dateFormatted') return false;
                    if (key === 'total') return showTotalCurve;
                    return selectedFournisseurs.includes(key);
                  })
                  .map((key, index) => {
                    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316', '#84CC16'];
                    return (
                      <Line
                        key={key}
                        type="monotone"
                        dataKey={key}
                        stroke={colors[index % colors.length]}
                        strokeWidth={key === 'total' ? 4 : 3}
                        dot={{ r: key === 'total' ? 5 : 4 }}
                        activeDot={{ r: key === 'total' ? 7 : 6 }}
                        name={key === 'total' ? 'Total' : key}
                      />
                    );
                  })}
              </LineChart>
            </ResponsiveContainer>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
