/**
 * Composant de la matrice principale du patrimoine global
 * Affiche le tableau avec édition inline des cellules
 */

import React from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Building2, Euro, Calculator } from 'lucide-react';
import type { PatrimoineMatrixProps } from '@/types/patrimoine';
import { formatMontant } from '@/utils/patrimoine-formatters';
import { getCellValue } from '@/utils/patrimoine-matrix';
import { CellEditor } from './CellEditor';

export const PatrimoineMatrix: React.FC<PatrimoineMatrixProps> = ({
  matrixData,
  editingCell,
  onCellEdit,
  onSaveEdit,
  onCancelEdit,
  onEditValueChange,
  loading
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement du patrimoine global...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-auto">
      <Table className="border-2 border-gray-300 rounded-lg overflow-hidden shadow-sm">
        <TableHeader>
          <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b-2 border-blue-200">
            {/* En-tête des clients */}
            <TableHead className="font-bold text-gray-800 py-3 px-3 min-w-[180px] sticky left-0 bg-blue-50 z-10 border-r-2 border-blue-300">
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Clients</span>
              </div>
            </TableHead>
            
            {/* En-têtes des fournisseurs */}
            {matrixData.fournisseurs.map((fournisseur) => (
              <TableHead 
                key={fournisseur.id} 
                className="text-center py-3 px-2 min-w-[120px] border-r border-gray-300"
              >
                <span className="font-bold text-gray-800 text-xs">
                  {fournisseur.nom}
                </span>
              </TableHead>
            ))}
            
            {/* En-tête du total */}
            <TableHead className="text-center py-3 px-3 min-w-[130px] bg-gradient-to-r from-green-50 to-green-100 border-l-2 border-green-400">
              <div className="flex items-center justify-center gap-1">
                <Euro className="h-4 w-4 text-green-600" />
                <span className="font-bold text-green-800 text-sm">Total Client</span>
              </div>
            </TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {/* Lignes des clients */}
          {matrixData.clients.map((client, clientIndex) => (
            <TableRow 
              key={client.id} 
              className={`hover:bg-blue-50/50 transition-colors border-b border-gray-200 ${
                clientIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'
              }`}
            >
              {/* Nom du client */}
              <TableCell className="font-bold py-2 px-3 sticky left-0 bg-white z-10 border-r-2 border-blue-300">
                <span className="text-gray-900 text-xs">{client.name}</span>
              </TableCell>
              
              {/* Cellules des montants par fournisseur */}
              {matrixData.fournisseurs.map((fournisseur) => {
                const cellValue = getCellValue(matrixData, client.id, fournisseur.id);
                const isEditing = editingCell?.clientId === client.id && 
                                 editingCell?.fournisseurId === fournisseur.id;

                return (
                  <TableCell 
                    key={fournisseur.id} 
                    className="text-center py-2 px-2 border-r border-gray-200"
                  >
                    <CellEditor
                      isEditing={isEditing}
                      value={editingCell?.value || ''}
                      displayValue={formatMontant(cellValue)}
                      onChange={onEditValueChange}
                      onSave={onSaveEdit}
                      onCancel={onCancelEdit}
                      onEdit={() => onCellEdit(client.id, fournisseur.id, cellValue)}
                    />
                  </TableCell>
                );
              })}
              
              {/* Total du client */}
              <TableCell className="text-center py-2 px-3 bg-gradient-to-r from-green-50 to-green-100 font-bold text-green-800 border-l-2 border-green-400">
                <div className="flex items-center justify-center gap-1">
                  <span className="text-sm">{formatMontant(client.total)}</span>
                </div>
              </TableCell>
            </TableRow>
          ))}
          
          {/* Ligne des totaux */}
          <TableRow className="bg-gradient-to-r from-green-100 to-green-200 border-t-3 border-green-400">
            {/* Label total */}
            <TableCell className="font-bold py-2 px-3 sticky left-0 bg-gradient-to-r from-green-100 to-green-200 z-10 border-r-2 border-green-400">
              <div className="flex items-center gap-1">
                <Calculator className="h-4 w-4 text-green-700" />
                <span className="text-green-900 text-xs">TOTAL</span>
              </div>
            </TableCell>
            
            {/* Totaux par fournisseur */}
            {matrixData.fournisseurs.map((fournisseur) => (
              <TableCell 
                key={fournisseur.id} 
                className="text-center py-2 px-2 font-bold text-green-800 border-r border-green-300"
              >
                <span className="text-xs bg-green-200 px-1 py-1 rounded">
                  {formatMontant(fournisseur.total)}
                </span>
              </TableCell>
            ))}
            
            {/* Grand total */}
            <TableCell className="text-center py-2 px-3 font-bold text-green-900 bg-gradient-to-r from-green-200 to-green-300 border-l-3 border-green-500">
              <div className="flex items-center justify-center gap-1">
                <Euro className="h-4 w-4 text-green-700" />
                <span className="text-sm">{formatMontant(matrixData.grandTotal)}</span>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
