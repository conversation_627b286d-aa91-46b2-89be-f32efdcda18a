/**
 * Hook unifié pour la gestion du tableau global du patrimoine
 * Centralise toute la logique métier et les interactions avec les données
 */

import { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useClientContext } from '@/contexts/ClientContext';
import { useGlobalPatrimoine } from './useGlobalPatrimoine';
import { usePatrimoineCache } from './usePatrimoineCache';
import type { MatrixData, CellEditState } from '@/types/patrimoine';
import { 
  buildPatrimoineMatrix, 
  getCellValue, 
  validateCellValue,
  findFournisseurById,
  validateFournisseurName
} from '@/utils/patrimoine-matrix';

/**
 * Hook principal pour le tableau global du patrimoine
 */
export const useGlobalPatrimoineMatrix = () => {
  // Contextes et hooks de données
  const { clients } = useClientContext();
  const {
    entries,
    loading: globalLoading,
    loadAllPatrimoine,
    upsertPatrimoineEntry,
    getClientTotals,
    getFournisseurTotals,
    getGrandTotal
  } = useGlobalPatrimoine();

  const {
    fournisseurs: cachedFournisseurs,
    loadPatrimoineData: loadCachedData
  } = usePatrimoineCache();

  // État local
  const [matrixData, setMatrixData] = useState<MatrixData>({
    clients: [],
    fournisseurs: [],
    matrix: new Map(),
    grandTotal: 0
  });

  const [editingCell, setEditingCell] = useState<CellEditState | null>(null);
  const [isBuilding, setIsBuilding] = useState(false);

  // État de chargement global
  const loading = globalLoading || isBuilding;

  /**
   * Construit la matrice à partir des données actuelles
   */
  const buildMatrix = useCallback(async () => {
    try {
      setIsBuilding(true);

      // 1. Utiliser les fournisseurs du cache si disponibles
      let fournisseursData = cachedFournisseurs;

      // Si pas de fournisseurs en cache, charger depuis la base
      if (!fournisseursData || fournisseursData.length === 0) {
        await loadCachedData(false);
        fournisseursData = cachedFournisseurs;
      }

      if (!fournisseursData || fournisseursData.length === 0) {
        console.warn('Aucun fournisseur disponible pour construire la matrice');
        return;
      }

      // 2. Construire la matrice
      const clientTotals = getClientTotals();
      const fournisseurTotals = getFournisseurTotals();
      const grandTotal = getGrandTotal();

      const newMatrixData = buildPatrimoineMatrix(
        entries,
        clients,
        fournisseursData,
        clientTotals,
        fournisseurTotals,
        grandTotal
      );

      setMatrixData(newMatrixData);

    } catch (error) {
      console.error('Erreur lors de la construction de la matrice:', error);
      toast.error('Erreur lors du chargement des données');
    } finally {
      setIsBuilding(false);
    }
  }, [
    cachedFournisseurs, 
    loadCachedData, 
    entries, 
    clients, 
    getClientTotals, 
    getFournisseurTotals, 
    getGrandTotal
  ]);

  /**
   * Démarre l'édition d'une cellule
   */
  const startCellEdit = useCallback((clientId: string, fournisseurId: string) => {
    const currentValue = getCellValue(matrixData, clientId, fournisseurId);
    setEditingCell({
      clientId,
      fournisseurId,
      value: currentValue === 0 ? '' : currentValue.toString()
    });
  }, [matrixData]);

  /**
   * Met à jour la valeur en cours d'édition
   */
  const updateEditingValue = useCallback((value: string) => {
    if (editingCell) {
      setEditingCell({ ...editingCell, value });
    }
  }, [editingCell]);

  /**
   * Sauvegarde l'édition d'une cellule
   */
  const saveCellEdit = useCallback(async () => {
    if (!editingCell) return false;

    const validation = validateCellValue(editingCell.value);
    if (!validation.isValid) {
      toast.error(validation.error || 'Valeur invalide');
      return false;
    }

    const fournisseur = findFournisseurById(matrixData.fournisseurs, editingCell.fournisseurId);
    if (!fournisseur) {
      toast.error('Fournisseur introuvable');
      return false;
    }

    try {
      const success = await upsertPatrimoineEntry(
        editingCell.clientId,
        fournisseur.nom,
        validation.numericValue
      );

      if (success) {
        await buildMatrix();
        setEditingCell(null);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast.error('Erreur lors de la sauvegarde');
      return false;
    }
  }, [editingCell, matrixData.fournisseurs, upsertPatrimoineEntry, buildMatrix]);

  /**
   * Annule l'édition d'une cellule
   */
  const cancelCellEdit = useCallback(() => {
    setEditingCell(null);
  }, []);

  /**
   * Ajoute un nouveau fournisseur
   */
  const addFournisseur = useCallback(async (name: string): Promise<boolean> => {
    const validation = validateFournisseurName(name, matrixData.fournisseurs);
    if (!validation.isValid) {
      toast.error(validation.error || 'Nom invalide');
      return false;
    }

    try {
      // Obtenir le prochain ordre d'affichage
      const { data: existingFournisseurs } = await supabase
        .from('patrimoine_fournisseurs')
        .select('ordre_affichage')
        .order('ordre_affichage', { ascending: false })
        .limit(1);

      const nextOrder = (existingFournisseurs?.[0]?.ordre_affichage || 0) + 1;

      const { error } = await supabase
        .from('patrimoine_fournisseurs')
        .insert({
          nom: name.trim(),
          ordre_affichage: nextOrder
        });

      if (error) throw error;

      toast.success('Fournisseur ajouté avec succès');
      await buildMatrix();
      return true;

    } catch (error) {
      console.error('Erreur lors de l\'ajout du fournisseur:', error);
      toast.error('Erreur lors de l\'ajout du fournisseur');
      return false;
    }
  }, [matrixData.fournisseurs, buildMatrix]);

  /**
   * Renomme un fournisseur
   */
  const renameFournisseur = useCallback(async (
    fournisseurId: string, 
    oldName: string, 
    newName: string
  ): Promise<boolean> => {
    const trimmedName = newName.trim();
    if (!trimmedName || trimmedName === oldName) return false;

    const validation = validateFournisseurName(trimmedName, matrixData.fournisseurs);
    if (!validation.isValid) {
      toast.error(validation.error || 'Nom invalide');
      return false;
    }

    try {
      // Mettre à jour le nom du fournisseur
      const { error: fournisseurError } = await supabase
        .from('patrimoine_fournisseurs')
        .update({ nom: trimmedName })
        .eq('id', fournisseurId);

      if (fournisseurError) throw fournisseurError;

      // Mettre à jour toutes les entrées de patrimoine associées
      const { error: patrimoineError } = await supabase
        .from('client_patrimoine')
        .update({ fournisseur: trimmedName })
        .eq('fournisseur', oldName);

      if (patrimoineError) throw patrimoineError;

      toast.success('Fournisseur renommé avec succès');
      await loadAllPatrimoine();
      await buildMatrix();
      return true;

    } catch (error) {
      console.error('Erreur lors du renommage du fournisseur:', error);
      toast.error('Erreur lors du renommage du fournisseur');
      return false;
    }
  }, [matrixData.fournisseurs, buildMatrix, loadAllPatrimoine]);

  /**
   * Supprime un fournisseur
   */
  const deleteFournisseur = useCallback(async (
    fournisseurId: string, 
    fournisseurNom: string
  ): Promise<boolean> => {
    try {
      // Supprimer d'abord toutes les entrées de patrimoine associées
      const { error: patrimoineError } = await supabase
        .from('client_patrimoine')
        .delete()
        .eq('fournisseur', fournisseurNom);

      if (patrimoineError) throw patrimoineError;

      // Puis supprimer le fournisseur
      const { error: fournisseurError } = await supabase
        .from('patrimoine_fournisseurs')
        .delete()
        .eq('id', fournisseurId);

      if (fournisseurError) throw fournisseurError;

      toast.success('Fournisseur supprimé avec succès');
      await loadAllPatrimoine();
      await buildMatrix();
      return true;

    } catch (error) {
      console.error('Erreur lors de la suppression du fournisseur:', error);
      toast.error('Erreur lors de la suppression du fournisseur');
      return false;
    }
  }, [buildMatrix, loadAllPatrimoine]);

  /**
   * Charge les données initiales
   */
  const loadData = useCallback(async () => {
    await loadAllPatrimoine();
  }, [loadAllPatrimoine]);

  // Effets
  useEffect(() => {
    if (entries.length >= 0 && clients.length > 0) {
      buildMatrix();
    }
  }, [entries, clients, buildMatrix]);

  return {
    // État
    matrixData,
    editingCell,
    loading,

    // Actions de cellule
    startCellEdit,
    updateEditingValue,
    saveCellEdit,
    cancelCellEdit,

    // Actions de fournisseur
    addFournisseur,
    renameFournisseur,
    deleteFournisseur,

    // Actions générales
    loadData,
    buildMatrix
  };
};
