import React, { useState, useMemo, useCallback, useEffect } from 'react';
import {
  AreaChart,
  Area,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { usePatrimoineEvolutionFournisseurs } from '@/hooks/usePatrimoineEvolutionFournisseurs';
import { usePatrimoineCache } from '@/hooks/usePatrimoineCache';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { TrendingUp, TrendingDown, Calendar, Euro, Users, BarChart3, ChevronUp, ChevronDown } from 'lucide-react';

interface PatrimoineEvolutionChartProps {
  className?: string;
  fournisseurs?: Array<{ id: string; nom: string; total: number }>;
}

export const PatrimoineEvolutionChart: React.FC<PatrimoineEvolutionChartProps> = ({
  className,
  fournisseurs = []
}) => {
  const [selectedFournisseurs, setSelectedFournisseurs] = useState<string[]>([]);
  const [showTotalCurve, setShowTotalCurve] = useState(true);

  const {
    snapshots,
    loading: loadingGlobal,
    getEvolutionMetrics,
    formatMontant,
    formatPercentage,
    hasData
  } = usePatrimoineEvolutionSimple();

  const {
    getEvolutionsByDate,
    getChartData,
    loading: loadingFournisseurs,
    formatMontant: formatMontantFournisseur
  } = usePatrimoineEvolutionFournisseurs();

  // Utiliser les mêmes données que les autres graphiques (cache patrimoine)
  const { entries: patrimoineData, fournisseurs: fournisseursCache, loadPatrimoineData } = usePatrimoineCache();

  // Debug pour voir les données
  console.log('🔍 PatrimoineEvolutionChart - fournisseursCache:', fournisseursCache);
  console.log('🔍 PatrimoineEvolutionChart - patrimoineData:', patrimoineData?.length || 0);

  // Charger les données au montage (comme dans PatrimoineAnalytics)
  useEffect(() => {
    console.log('🔄 Chargement initial des données patrimoine...');
    loadPatrimoineData(false); // Utilise le cache si valide
  }, []); // Pas de dépendances pour éviter la boucle

  const loading = loadingGlobal || loadingFournisseurs;
  const metrics = getEvolutionMetrics();

  // Debug pour comprendre les totaux
  useEffect(() => {
    console.log('💰 ANALYSE DES TOTAUX:');
    console.log('  - Total évolution (affiché):', metrics.currentTotal);
    console.log('  - Snapshots évolution:', snapshots);

    if (patrimoineData && patrimoineData.length > 0) {
      const totalPatrimoineReel = patrimoineData.reduce((sum, entry) => sum + (entry.montant || 0), 0);
      console.log('  - Total patrimoine réel (client_patrimoine):', totalPatrimoineReel);
      console.log('  - Différence:', Math.abs(metrics.currentTotal - totalPatrimoineReel));
    }
  }, [metrics.currentTotal, snapshots, patrimoineData]);

  // Créer une liste de fournisseurs à partir des données patrimoine si le cache est vide
  const fournisseursDisponibles = useMemo(() => {
    if (fournisseursCache && fournisseursCache.length > 0) {
      console.log('📋 Utilisation du cache fournisseurs:', fournisseursCache);
      return fournisseursCache;
    }

    // Fallback : extraire les fournisseurs des données patrimoine
    if (patrimoineData && patrimoineData.length > 0) {
      const fournisseursUniques = new Map<string, { nom: string; total: number }>();

      patrimoineData.forEach(entry => {
        const nom = entry.fournisseur || 'Inconnu';
        const montant = entry.montant || 0;

        if (fournisseursUniques.has(nom)) {
          fournisseursUniques.get(nom)!.total += montant;
        } else {
          fournisseursUniques.set(nom, { nom, total: montant });
        }
      });

      const fournisseursList = Array.from(fournisseursUniques.values());
      console.log('📋 Fournisseurs extraits des données patrimoine:', fournisseursList);
      return fournisseursList;
    }

    console.log('📋 Aucun fournisseur disponible');
    return [];
  }, [fournisseursCache, patrimoineData]);

  // Debug supplémentaire
  useEffect(() => {
    console.log('🔍 PatrimoineEvolutionChart - État des données:');
    console.log('  - patrimoineData:', patrimoineData?.length || 0, 'entrées');
    console.log('  - fournisseursCache:', fournisseursCache?.length || 0, 'fournisseurs');
    console.log('  - fournisseursDisponibles:', fournisseursDisponibles?.length || 0, 'disponibles');
  }, [patrimoineData, fournisseursCache, fournisseursDisponibles]);

  // Couleurs pour les fournisseurs
  const fournisseurColors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ];

  // Calculer les totaux par fournisseur à partir des VRAIES données (cache patrimoine)
  const getTotalsByFournisseur = useCallback(() => {
    const totaux: Record<string, number> = {};

    console.log('🔍 Calcul totaux - Données patrimoine:', patrimoineData?.length || 0);

    if (patrimoineData) {
      patrimoineData.forEach(entry => {
        const fournisseurNom = entry.fournisseur || 'Inconnu';
        totaux[fournisseurNom] = (totaux[fournisseurNom] || 0) + (entry.montant || 0);
      });
    }

    console.log('🔍 Totaux calculés (VRAIES données):', totaux);
    return totaux;
  }, [patrimoineData]);

  // Préparer les données combinées pour le graphique
  const chartData = useMemo(() => {
    if (selectedFournisseurs.length === 0) {
      // Mode global uniquement - données simplifiées
      return snapshots.map(snapshot => ({
        date: snapshot.date,
        dateFormatted: new Date(snapshot.date).toLocaleDateString('fr-FR', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        total: snapshot.total
      }));
    }

    // Mode avec fournisseurs sélectionnés - utiliser les VRAIES données du tableau global
    const totauxActuels = getTotalsByFournisseur();
    const totalGlobalActuel = Object.values(totauxActuels).reduce((sum, montant) => sum + montant, 0);

    console.log('📊 Totaux actuels par fournisseur:', totauxActuels);
    console.log('📊 Total global actuel:', totalGlobalActuel);

    return snapshots.map(snapshot => {
      const result: any = {
        date: snapshot.date,
        dateFormatted: new Date(snapshot.date).toLocaleDateString('fr-FR', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        total: snapshot.total
      };

      // Pour chaque fournisseur sélectionné, calculer sa part proportionnelle
      // basée sur les VRAIES données du tableau global
      selectedFournisseurs.forEach(fournisseurNom => {
        const montantActuelFournisseur = totauxActuels[fournisseurNom] || 0;

        if (totalGlobalActuel > 0) {
          // Calculer la proportion réelle du fournisseur
          const proportionFournisseur = montantActuelFournisseur / totalGlobalActuel;

          // Appliquer cette proportion au total de cette date d'évolution
          const montantFournisseurALaDate = Math.round(snapshot.total * proportionFournisseur);
          result[fournisseurNom] = montantFournisseurALaDate;

          console.log(`💰 ${snapshot.date} - ${fournisseurNom}: ${montantFournisseurALaDate}€ (${(proportionFournisseur * 100).toFixed(1)}% de ${snapshot.total}€) [Actuel: ${montantActuelFournisseur}€]`);
        } else {
          result[fournisseurNom] = 0;
        }
      });

      return result;
    });
  }, [snapshots, selectedFournisseurs, getTotalsByFournisseur]);

  const handleFournisseurToggle = useCallback((fournisseurId: string) => {
    console.log('🔄 TOGGLE appelé avec:', fournisseurId);
    console.log('📋 État AVANT:', selectedFournisseurs);

    // Créer directement le nouvel état sans dépendre du précédent
    const currentSelection = [...selectedFournisseurs];
    const isCurrentlySelected = currentSelection.includes(fournisseurId);

    let newSelection: string[];
    if (isCurrentlySelected) {
      // Retirer le fournisseur
      newSelection = currentSelection.filter(id => id !== fournisseurId);
      console.log('➖ RETRAIT de:', fournisseurId);
    } else {
      // Ajouter le fournisseur
      newSelection = [...currentSelection, fournisseurId];
      console.log('➕ AJOUT de:', fournisseurId);
    }

    console.log('🎯 NOUVELLE sélection:', newSelection);
    setSelectedFournisseurs(newSelection);
  }, [selectedFournisseurs]);

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-blue-600">
            <Calendar className="h-16 w-16 mx-auto mb-4 text-blue-300" />
            <p className="text-lg font-medium mb-2">Aucun point d'évolution</p>
            <p className="text-sm text-blue-500">Ajoutez des points dans le tableau global pour voir l'évolution</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
          <div className="flex items-center gap-3">
            {metrics.totalGrowth !== 0 && (
              <Badge
                variant={metrics.totalGrowth >= 0 ? "default" : "destructive"}
                className={metrics.totalGrowth >= 0 ? "bg-blue-100 text-blue-800" : "bg-red-100 text-red-800"}
              >
                {metrics.totalGrowth >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(metrics.totalGrowth)}
              </Badge>
            )}
            <Badge variant="outline" className="bg-blue-50 text-blue-800">
              <Euro className="h-3 w-3 mr-1" />
              {formatMontant(metrics.currentTotal)}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Sélecteur de fournisseurs - Repositionné au-dessus du graphique */}
        {fournisseursDisponibles.length > 0 && (
          <div className="flex items-center justify-between p-3 bg-gray-50/50 rounded-lg border border-gray-200/50">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500"></div>
              <span className="text-sm font-medium text-gray-700">Courbes Fournisseurs</span>
              {selectedFournisseurs.length > 0 && (
                <div className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full font-medium">
                  {selectedFournisseurs.length} sélectionné{selectedFournisseurs.length > 1 ? 's' : ''}
                </div>
              )}
            </div>

            {/* Actions rapides */}
            <div className="flex gap-2 items-center">
              {/* Toggle courbe totale */}
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-600">Total</span>
                <button
                  onClick={() => setShowTotalCurve(!showTotalCurve)}
                  className={`relative inline-flex h-4 w-7 items-center rounded-full transition-colors ${
                    showTotalCurve ? 'bg-gray-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      showTotalCurve ? 'translate-x-3.5' : 'translate-x-0.5'
                    }`}
                  />
                </button>
              </div>

              <div className="w-px h-4 bg-gray-300"></div>

              <button
                type="button"
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('🔵 BOUTON TOUT cliqué');
                  const allIds = fournisseursDisponibles.map(f => f.nom);
                  console.log('🔵 Sélection de tous les IDs:', allIds);
                  setSelectedFournisseurs(allIds);
                }}
                className="text-xs py-1 px-2 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors"
              >
                Tout
              </button>
              <button
                type="button"
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('🔴 BOUTON AUCUN cliqué');
                  setSelectedFournisseurs([]);
                }}
                className="text-xs py-1 px-2 bg-gray-50 text-gray-600 rounded hover:bg-gray-100 transition-colors"
              >
                Aucun
              </button>
            </div>
          </div>
        )}

        {/* Liste des fournisseurs sélectionnables */}
        {fournisseursDisponibles.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
            {fournisseursDisponibles.map((fournisseur, index) => {
              // Utiliser le nom comme identifiant unique
              const fournisseurId = fournisseur.nom;
              const isSelected = selectedFournisseurs.includes(fournisseurId);
              const color = fournisseurColors[index % fournisseurColors.length];

              return (
                <div
                  key={fournisseurId}
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('🎯 Clic sur fournisseur:', fournisseurId);
                    handleFournisseurToggle(fournisseurId);
                  }}
                  className={`flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    isSelected
                      ? 'bg-blue-50 border border-blue-200 shadow-sm'
                      : 'hover:bg-gray-50 border border-gray-200/50'
                  }`}
                >
                  <div
                    className={`w-3 h-3 rounded-full transition-all duration-200 ${
                      isSelected ? 'scale-110 shadow-md' : 'opacity-60'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                  <span className={`text-xs truncate flex-1 transition-colors duration-200 ${
                    isSelected ? 'text-blue-700 font-medium' : 'text-gray-600'
                  }`}>
                    {fournisseur.nom.length > 12 ? fournisseur.nom.substring(0, 12) + '...' : fournisseur.nom}
                  </span>
                  {isSelected && (
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* Graphique d'évolution - Zone libre */}
        <div className="relative">

          {/* Graphique d'évolution - Design moderne et smooth */}
          <div className="h-96 bg-gradient-to-br from-gray-50/30 to-blue-50/20 rounded-2xl p-6 border border-gray-100/50">
            <ResponsiveContainer width="100%" height="100%">
              {selectedFournisseurs.length > 0 ? (
                <LineChart
                  data={chartData}
                  margin={{ top: 30, right: 40, left: 30, bottom: 30 }}
                  style={{ filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.05))' }}
                >
                  <defs>
                    <linearGradient id="gridGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#E5E7EB" stopOpacity={0.8} />
                      <stop offset="100%" stopColor="#E5E7EB" stopOpacity={0.2} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid
                    strokeDasharray="2 4"
                    stroke="url(#gridGradient)"
                    strokeWidth={1}
                    opacity={0.6}
                  />
                  <XAxis
                    dataKey="dateFormatted"
                    stroke="#9CA3AF"
                    fontSize={11}
                    tickLine={false}
                    axisLine={false}
                    tick={{ fill: '#6B7280', fontSize: 11 }}
                    dy={10}
                  />
                  <YAxis
                    stroke="#9CA3AF"
                    fontSize={11}
                    tickLine={false}
                    axisLine={false}
                    tick={{ fill: '#6B7280', fontSize: 11 }}
                    dx={-10}
                    domain={showTotalCurve ? ['auto', 'auto'] : ['dataMin', 'dataMax']}
                    tickFormatter={(value) => {
                      if (value >= 1000000) {
                        return `${(value / 1000000).toFixed(1)}M€`;
                      } else if (value >= 1000) {
                        return `${(value / 1000).toFixed(0)}K€`;
                      }
                      return `${value}€`;
                    }}
                  />
                  <Tooltip
                    content={({ active, payload, label }) => {
                      if (active && payload && payload.length) {
                        const sortedPayload = [...payload].sort((a, b) => {
                          if (a.dataKey === 'total') return -1;
                          if (b.dataKey === 'total') return 1;
                          return (b.value as number) - (a.value as number);
                        });

                        return (
                          <div className="bg-white/95 backdrop-blur-xl p-4 border-0 rounded-2xl shadow-2xl max-w-sm">
                            <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-100">
                              <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                              <span className="font-semibold text-gray-800 text-sm">{label}</span>
                            </div>
                            <div className="space-y-2.5">
                              {sortedPayload.map((entry, index) => {
                                const isTotal = entry.dataKey === 'total';
                                const value = entry.value as number;

                                if (value === 0 && !isTotal) return null;

                                return (
                                  <div key={`${entry.dataKey}-${index}`} className="flex items-center justify-between gap-4">
                                    <div className="flex items-center gap-2.5 min-w-0 flex-1">
                                      <div
                                        className={`w-3 h-3 rounded-full shadow-sm flex-shrink-0 ${isTotal ? 'ring-2 ring-gray-300 ring-offset-1' : ''}`}
                                        style={{ backgroundColor: entry.color }}
                                      />
                                      <span className={`text-sm truncate ${isTotal ? 'font-bold text-gray-900' : 'font-medium text-gray-700'}`}>
                                        {isTotal ? 'Total Global' : entry.dataKey}
                                      </span>
                                    </div>
                                    <span className={`text-sm font-bold whitespace-nowrap ${isTotal ? 'text-gray-900' : 'text-gray-600'}`}>
                                      {formatMontant(value)}
                                    </span>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Legend
                    wrapperStyle={{
                      paddingTop: '20px',
                      fontSize: '12px'
                    }}
                    iconType="line"
                  />

                  {/* Ligne du total global - style premium smooth */}
                  {showTotalCurve && (
                    <Line
                      type="monotone"
                      dataKey="total"
                      stroke="#1F2937"
                      strokeWidth={4}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      dot={{
                        fill: '#1F2937',
                        strokeWidth: 0,
                        r: 6,
                        filter: 'drop-shadow(0 2px 4px rgba(31, 41, 55, 0.3))'
                      }}
                      activeDot={{
                        r: 10,
                        stroke: '#1F2937',
                        strokeWidth: 4,
                        fill: '#ffffff',
                        filter: 'drop-shadow(0 4px 8px rgba(31, 41, 55, 0.4))'
                      }}
                      name="Total Global"
                    />
                  )}

                  {/* Lignes des fournisseurs sélectionnés - styles smooth et modernes */}
                  {selectedFournisseurs.map((fournisseurNom, index) => {
                    const fournisseur = fournisseursDisponibles.find(f => f.nom === fournisseurNom);
                    if (!fournisseur) {
                      console.log('⚠️ Fournisseur non trouvé:', fournisseurNom);
                      return null;
                    }

                    const color = fournisseurColors[index % fournisseurColors.length];
                    console.log('📊 Affichage courbe pour:', fournisseurNom, 'couleur:', color);

                    return (
                      <Line
                        key={fournisseurNom}
                        type="monotone"
                        dataKey={fournisseur.nom}
                        stroke={color}
                        strokeWidth={3}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        dot={{
                          fill: color,
                          strokeWidth: 0,
                          r: 5,
                          filter: `drop-shadow(0 2px 4px ${color}40)`
                        }}
                        activeDot={{
                          r: 8,
                          stroke: color,
                          strokeWidth: 3,
                          fill: '#ffffff',
                          filter: `drop-shadow(0 3px 6px ${color}60)`
                        }}
                        name={fournisseur.nom.length > 18 ? fournisseur.nom.substring(0, 15) + '...' : fournisseur.nom}
                        connectNulls={false}
                      />
                    );
                  })}
              </LineChart>
            ) : (
              <AreaChart
                data={chartData}
                margin={{ top: 30, right: 40, left: 30, bottom: 30 }}
                style={{ filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.05))' }}
              >
                <defs>
                  <linearGradient id="patrimoineGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.4} />
                    <stop offset="50%" stopColor="#3B82F6" stopOpacity={0.1} />
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.02} />
                  </linearGradient>
                  <linearGradient id="gridGradientSimple" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#E5E7EB" stopOpacity={0.8} />
                    <stop offset="100%" stopColor="#E5E7EB" stopOpacity={0.2} />
                  </linearGradient>
                </defs>
                <CartesianGrid
                  strokeDasharray="2 4"
                  stroke="url(#gridGradientSimple)"
                  strokeWidth={1}
                  opacity={0.6}
                />
                <XAxis
                  dataKey="dateFormatted"
                  stroke="#9CA3AF"
                  fontSize={11}
                  tickLine={false}
                  axisLine={false}
                  tick={{ fill: '#6B7280', fontSize: 11 }}
                  dy={10}
                />
                <YAxis
                  stroke="#9CA3AF"
                  fontSize={11}
                  tickLine={false}
                  axisLine={false}
                  tick={{ fill: '#6B7280', fontSize: 11 }}
                  dx={-10}
                  tickFormatter={(value) => {
                    if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M€`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(0)}K€`;
                    }
                    return `${value}€`;
                  }}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-white/95 backdrop-blur-xl p-4 border-0 rounded-2xl shadow-2xl">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                            <span className="font-semibold text-gray-800 text-sm">{label}</span>
                          </div>
                          <div className="text-lg font-bold text-blue-600">
                            {formatMontant(payload[0].value as number)}
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="total"
                  stroke="#3B82F6"
                  strokeWidth={4}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="url(#patrimoineGradient)"
                  dot={{
                    fill: '#3B82F6',
                    strokeWidth: 0,
                    r: 6,
                    filter: 'drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3))'
                  }}
                  activeDot={{
                    r: 10,
                    stroke: '#3B82F6',
                    strokeWidth: 4,
                    fill: '#ffffff',
                    filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.4))'
                  }}
                />
              </AreaChart>
            )}
          </ResponsiveContainer>
          </div>
        </div>


      </CardContent>
    </Card>
  );
};
