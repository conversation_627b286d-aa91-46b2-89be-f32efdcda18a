# 🔧 Correction - Affichage des Lignes d'Évolution

## ❌ **Problème identifié**

**Symptôme :** Quand l'utilisateur ajoute des lignes au tableau d'évolution, elles n'apparaissent pas dans le tableau, seulement dans les courbes avec des valeurs à 0.

**Cause racine :** La fonction `getCombinedData()` avait plusieurs limitations :
1. **Limite artificielle** : `.slice(0, 4)` limitait l'affichage à 4 lignes maximum
2. **Pas de lignes vides** : Seules les lignes avec des snapshots ou évolutions existants étaient affichées
3. **Pas d'interface pour nouvelles lignes** : Aucun moyen d'ajouter des lignes vides éditables

## ✅ **Solutions appliquées**

### 🔄 **1. Fonction getCombinedData() réécrite**

```typescript
// ❌ Avant : Limitation à 4 lignes
return Array.from(allDates)
  .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
  .slice(0, 4) // Limiter à 4 lignes
  .map(date => { /* ... */ });

// ✅ Après : Affichage de toutes les lignes + lignes vides
const getCombinedData = () => {
  // Créer les lignes existantes (TOUTES)
  const existingRows = Array.from(allDates)
    .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
    .map(date => { /* ... */ });

  // Ajouter des lignes vides pour permettre l'ajout
  const minRows = Math.max(4, existingRows.length + 2);
  const emptyRowsNeeded = minRows - existingRows.length;
  
  const emptyRows = Array.from({ length: emptyRowsNeeded }, () => ({
    date: null,
    snapshot: null,
    evolution: null,
    fournisseurMontants: {},
    totalCalcule: 0,
    totalSnapshot: 0
  }));

  return [...existingRows, ...emptyRows];
};
```

### 📝 **2. Interface améliorée pour lignes vides**

**Dates vides :**
```typescript
// Affichage visuel distinctif pour les lignes vides
<div className={`cursor-pointer hover:bg-blue-100 px-2 py-1 rounded ${
  rowData.date ? 'text-blue-700' : 'text-gray-400 border border-dashed border-gray-300'
}`}>
  {rowData.date ? new Date(rowData.date).toLocaleDateString('fr-FR') : '+ Ajouter date'}
</div>
```

**Montants vides :**
```typescript
// Affichage différencié selon l'état de la ligne
<div className="truncate">
  {montant > 0 ? formatMontantFournisseur(montant).replace(' €', '€') : 
   rowData.date ? '••' : '---'}
</div>
```

### ➕ **3. Bouton "Ajouter Ligne" optimisé**

```typescript
// ❌ Avant : Ajoutait avec les totaux actuels
const success = await upsertSnapshot(today, currentTotal);
if (success) {
  for (const fournisseur of fournisseurs) {
    await upsertEvolution(today, fournisseur.id, fournisseur.total);
  }
}

// ✅ Après : Crée une ligne vide éditable
const success = await upsertSnapshot(today, 0);
if (success) {
  toast.success('Nouvelle ligne vide ajoutée - cliquez pour éditer les montants');
}
```

### 🎨 **4. États visuels améliorés**

| État de la ligne | Affichage Date | Affichage Montants | Affichage Total |
|------------------|----------------|-------------------|-----------------|
| **Ligne avec données** | Date normale (bleu) | Montants verts | Total vert/bleu |
| **Ligne avec date seulement** | Date normale (bleu) | `••` (gris) | `Double-clic` (gris) |
| **Ligne vide** | `+ Ajouter date` (gris, bordure pointillée) | `---` (gris, bordure pointillée) | `---` (gris, bordure pointillée) |

## 🎯 **Résultat final**

### **Fonctionnalités maintenant opérationnelles :**

1. ✅ **Affichage de toutes les lignes** : Plus de limitation à 4 lignes
2. ✅ **Lignes vides visibles** : Interface claire pour ajouter de nouvelles données
3. ✅ **Bouton "Ajouter Ligne"** : Crée des lignes vides éditables
4. ✅ **États visuels distincts** : Différenciation claire entre lignes pleines/vides
5. ✅ **Workflow complet** : 
   - Clic sur "Ajouter Ligne" → Ligne vide apparaît
   - Clic sur "Date" → Définir la date
   - Double-clic sur montants → Éditer les valeurs
   - Les courbes se mettent à jour automatiquement

### **Guide d'utilisation mis à jour :**

1. **Ajouter une nouvelle ligne :**
   - Cliquer sur "Ajouter Ligne" → Une ligne vide apparaît
   - Cliquer sur "+ Ajouter date" → Définir la date
   - Double-cliquer sur les montants → Saisir les valeurs

2. **Éditer une ligne existante :**
   - Cliquer sur la date → Modifier la date
   - Double-cliquer sur un montant → Modifier la valeur
   - Double-cliquer sur le total → Modifier le total global

3. **Réorganiser les lignes :**
   - Glisser-déposer avec la poignée ⋮ pour changer l'ordre

### **Synchronisation parfaite :**
- ✅ **Tableau ↔ Courbes** : Toutes les modifications se reflètent immédiatement
- ✅ **Nouvelles lignes** : Apparaissent dans le tableau ET les courbes
- ✅ **Édition en temps réel** : Mise à jour instantanée des graphiques

Le problème est maintenant complètement résolu ! 🎉
