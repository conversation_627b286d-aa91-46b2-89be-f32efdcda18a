/**
 * Utilitaires de calcul pour le patrimoine
 * Centralise toute la logique de calcul complexe
 */

import type { 
  PatrimoineEntry, 
  FournisseurData, 
  EvolutionSnapshot, 
  ChartDataPoint,
  EvolutionMetrics 
} from '@/types/patrimoine';
import { formatDateForChart } from './patrimoine-formatters';

/**
 * Extrait les fournisseurs uniques des données patrimoine
 */
export const extractFournisseursFromPatrimoine = (
  patrimoineData: PatrimoineEntry[]
): FournisseurData[] => {
  const fournisseursUniques = new Map<string, { nom: string; total: number }>();

  patrimoineData.forEach(entry => {
    const nom = entry.fournisseur || 'Inconnu';
    const montant = entry.montant || 0;

    if (fournisseursUniques.has(nom)) {
      fournisseursUniques.get(nom)!.total += montant;
    } else {
      fournisseursUniques.set(nom, { nom, total: montant });
    }
  });

  return Array.from(fournisseursUniques.values()).map((item, index) => ({
    id: `extracted-${index}`,
    nom: item.nom,
    ordre_affichage: index,
    actif: true,
    total: item.total
  }));
};

/**
 * Calcule les totaux par fournisseur
 */
export const calculateTotalsByFournisseur = (
  patrimoineData: PatrimoineEntry[]
): Record<string, number> => {
  const totaux: Record<string, number> = {};

  patrimoineData.forEach(entry => {
    const fournisseurNom = entry.fournisseur || 'Inconnu';
    totaux[fournisseurNom] = (totaux[fournisseurNom] || 0) + (entry.montant || 0);
  });

  return totaux;
};

/**
 * Calcule le total global du patrimoine
 */
export const calculateTotalPatrimoine = (patrimoineData: PatrimoineEntry[]): number => {
  return patrimoineData.reduce((sum, entry) => sum + (entry.montant || 0), 0);
};

/**
 * Prépare les données pour le graphique global (sans fournisseurs)
 */
export const prepareGlobalChartData = (snapshots: EvolutionSnapshot[]): ChartDataPoint[] => {
  return snapshots.map(snapshot => ({
    date: snapshot.date,
    dateFormatted: formatDateForChart(snapshot.date),
    total: snapshot.total
  }));
};

/**
 * Prépare les données pour le graphique avec fournisseurs
 */
export const prepareFournisseursChartData = (
  snapshots: EvolutionSnapshot[],
  selectedFournisseurs: string[],
  patrimoineData: PatrimoineEntry[]
): ChartDataPoint[] => {
  const totauxActuels = calculateTotalsByFournisseur(patrimoineData);
  const totalGlobalActuel = Object.values(totauxActuels).reduce((sum, montant) => sum + montant, 0);

  return snapshots.map(snapshot => {
    const result: ChartDataPoint = {
      date: snapshot.date,
      dateFormatted: formatDateForChart(snapshot.date),
      total: snapshot.total
    };

    // Calculer la part proportionnelle de chaque fournisseur
    selectedFournisseurs.forEach(fournisseurNom => {
      const montantActuelFournisseur = totauxActuels[fournisseurNom] || 0;

      if (totalGlobalActuel > 0) {
        const proportionFournisseur = montantActuelFournisseur / totalGlobalActuel;
        const montantFournisseurALaDate = Math.round(snapshot.total * proportionFournisseur);
        result[fournisseurNom] = montantFournisseurALaDate;
      } else {
        result[fournisseurNom] = 0;
      }
    });

    return result;
  });
};

/**
 * Calcule les métriques d'évolution
 */
export const calculateEvolutionMetrics = (snapshots: EvolutionSnapshot[]): EvolutionMetrics => {
  if (snapshots.length < 2) {
    return {
      totalGrowth: 0,
      averageGrowth: 0,
      bestSnapshot: snapshots[0] || null,
      worstSnapshot: snapshots[0] || null,
      currentTotal: snapshots[0]?.total || 0,
      previousTotal: 0
    };
  }

  const sortedSnapshots = [...snapshots].sort((a, b) => 
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );
  
  const firstSnapshot = sortedSnapshots[0];
  const lastSnapshot = sortedSnapshots[sortedSnapshots.length - 1];
  
  const totalGrowth = firstSnapshot.total > 0 
    ? ((lastSnapshot.total - firstSnapshot.total) / firstSnapshot.total) * 100 
    : 0;

  const averageGrowth = sortedSnapshots.length > 1 
    ? totalGrowth / (sortedSnapshots.length - 1) 
    : 0;

  const bestSnapshot = sortedSnapshots.reduce((best, current) => 
    current.total > best.total ? current : best, sortedSnapshots[0]);
  
  const worstSnapshot = sortedSnapshots.reduce((worst, current) => 
    current.total < worst.total ? current : worst, sortedSnapshots[0]);

  return {
    totalGrowth,
    averageGrowth,
    bestSnapshot,
    worstSnapshot,
    currentTotal: lastSnapshot.total,
    previousTotal: sortedSnapshots.length > 1 ? sortedSnapshots[sortedSnapshots.length - 2].total : 0
  };
};
