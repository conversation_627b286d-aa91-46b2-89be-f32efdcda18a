/**
 * Composant de gestion des fournisseurs
 * Interface pour ajouter, modifier et supprimer des fournisseurs
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Plus, Edit3, Trash2, Settings } from 'lucide-react';
import type { FournisseurManagerProps } from '@/types/patrimoine';
import { formatMontant } from '@/utils/patrimoine-formatters';

export const FournisseurManager: React.FC<FournisseurManagerProps> = ({
  isOpen,
  onClose,
  fournisseurs,
  onAddFournisseur,
  onRenameFournisseur,
  onDeleteFournisseur
}) => {
  const [newFournisseurName, setNewFournisseurName] = useState('');
  const [editingFournisseur, setEditingFournisseur] = useState<{ id: string; nom: string } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAddFournisseur = async () => {
    if (!newFournisseurName.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const success = await onAddFournisseur(newFournisseurName);
      if (success) {
        setNewFournisseurName('');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRenameFournisseur = async (fournisseurId: string, oldName: string, newName: string) => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      const success = await onRenameFournisseur(fournisseurId, oldName, newName);
      if (success) {
        setEditingFournisseur(null);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteFournisseur = async (fournisseurId: string, fournisseurNom: string) => {
    if (isSubmitting) return;

    const confirmed = window.confirm(
      `Êtes-vous sûr de vouloir supprimer le fournisseur "${fournisseurNom}" ?\n\n` +
      'Toutes les entrées de patrimoine associées seront également supprimées.'
    );

    if (!confirmed) return;

    setIsSubmitting(true);
    try {
      await onDeleteFournisseur(fournisseurId, fournisseurNom);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-600" />
            Gestion des Fournisseurs
          </DialogTitle>
          <DialogDescription>
            Gérer la liste des fournisseurs de patrimoine : ajouter, modifier ou supprimer des fournisseurs.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 flex-1 min-h-0">
          {/* Ajouter un nouveau fournisseur */}
          <div className="space-y-2">
            <h3 className="font-medium text-gray-700">Ajouter un fournisseur</h3>
            <div className="flex gap-3">
              <Input
                placeholder="Nom du nouveau fournisseur..."
                value={newFournisseurName}
                onChange={(e) => setNewFournisseurName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddFournisseur();
                  }
                }}
                className="flex-1"
                disabled={isSubmitting}
              />
              <Button
                onClick={handleAddFournisseur}
                disabled={!newFournisseurName.trim() || isSubmitting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Ajouter
              </Button>
            </div>
          </div>

          {/* Liste des fournisseurs existants */}
          <div className="space-y-2 flex-1 min-h-0">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-700">Fournisseurs existants</h3>
              <Badge variant="outline" className="text-xs">
                {fournisseurs.length} fournisseur{fournisseurs.length > 1 ? 's' : ''}
              </Badge>
            </div>
            
            <div className="space-y-2 max-h-96 overflow-y-auto pr-2">
              {fournisseurs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Settings className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p className="text-sm">Aucun fournisseur configuré</p>
                  <p className="text-xs text-gray-400">Ajoutez votre premier fournisseur ci-dessus</p>
                </div>
              ) : (
                fournisseurs.map((fournisseur) => (
                  <div 
                    key={fournisseur.id} 
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      {editingFournisseur?.id === fournisseur.id ? (
                        <Input
                          value={editingFournisseur.nom}
                          onChange={(e) => setEditingFournisseur({ 
                            ...editingFournisseur, 
                            nom: e.target.value 
                          })}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              handleRenameFournisseur(
                                fournisseur.id, 
                                fournisseur.nom, 
                                editingFournisseur.nom
                              );
                            }
                            if (e.key === 'Escape') {
                              e.preventDefault();
                              setEditingFournisseur(null);
                            }
                          }}
                          onBlur={() => {
                            if (editingFournisseur.nom !== fournisseur.nom) {
                              handleRenameFournisseur(
                                fournisseur.id, 
                                fournisseur.nom, 
                                editingFournisseur.nom
                              );
                            } else {
                              setEditingFournisseur(null);
                            }
                          }}
                          className="w-48"
                          autoFocus
                          disabled={isSubmitting}
                        />
                      ) : (
                        <span
                          className="font-medium cursor-pointer hover:text-blue-600 transition-colors truncate"
                          onClick={() => setEditingFournisseur({ 
                            id: fournisseur.id, 
                            nom: fournisseur.nom 
                          })}
                          title={fournisseur.nom}
                        >
                          {fournisseur.nom}
                        </span>
                      )}
                      
                      <Badge variant="outline" className="bg-green-50 text-green-700 text-xs">
                        {formatMontant(fournisseur.total)}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-1 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingFournisseur({ 
                          id: fournisseur.id, 
                          nom: fournisseur.nom 
                        })}
                        className="text-blue-600 hover:bg-blue-50 h-8 w-8 p-0"
                        disabled={isSubmitting}
                        title="Modifier le nom"
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteFournisseur(fournisseur.id, fournisseur.nom)}
                        className="text-red-600 hover:bg-red-50 h-8 w-8 p-0"
                        disabled={isSubmitting}
                        title="Supprimer le fournisseur"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
