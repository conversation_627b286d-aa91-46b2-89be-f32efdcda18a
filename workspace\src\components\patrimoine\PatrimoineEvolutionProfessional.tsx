/**
 * Composant principal professionnel pour l'évolution du patrimoine
 * Interface complète avec courbes temps réel et contrôles avancés
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronDown, 
  ChevronUp, 
  Save, 
  TrendingUp, 
  Calendar,
  BarChart3,
  Zap
} from 'lucide-react';
import { usePatrimoineEvolutionProfessional } from '@/hooks/usePatrimoineEvolutionProfessional';
import { EvolutionChartProfessional } from './EvolutionChartProfessional';

interface PatrimoineEvolutionProfessionalProps {
  currentTotal: number;
}

export const PatrimoineEvolutionProfessional: React.FC<PatrimoineEvolutionProfessionalProps> = ({
  currentTotal
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const {
    // Données
    snapshots,
    chartData,
    metrics,
    fournisseursDisponibles,
    
    // État
    loading,
    hasData,
    
    // Sélection courbes
    selectedFournisseurs,
    showTotalCurve,
    
    // Actions
    freezeCurrentTotal,
    toggleFournisseur,
    selectAllFournisseurs,
    clearSelection,
    setShowTotalCurve
  } = usePatrimoineEvolutionProfessional();

  const formatMontant = (montant: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(montant);
  };

  return (
    <div className="space-y-4">
      {/* En-tête avec toggle */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-3 text-lg font-semibold text-blue-700 hover:text-blue-900 transition-colors group"
        >
          <div className="flex items-center gap-2">
            {isExpanded ? (
              <ChevronUp className="h-5 w-5 group-hover:scale-110 transition-transform" />
            ) : (
              <ChevronDown className="h-5 w-5 group-hover:scale-110 transition-transform" />
            )}
            <TrendingUp className="h-5 w-5" />
            <span>Évolution Temporelle du Patrimoine</span>
          </div>
          {hasData && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
              {snapshots.length} point{snapshots.length > 1 ? 's' : ''} d'évolution
            </Badge>
          )}
        </button>

        {/* Actions rapides */}
        {isExpanded && (
          <div className="flex items-center gap-2">
            <Button
              onClick={freezeCurrentTotal}
              disabled={loading || currentTotal <= 0}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
              size="sm"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              Figer Total Actuel
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                {formatMontant(currentTotal)}
              </Badge>
            </Button>
          </div>
        )}
      </div>

      {/* Contenu principal */}
      {isExpanded && (
        <Card className="border-blue-200 bg-gradient-to-br from-blue-50/30 to-indigo-50/20">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <BarChart3 className="h-5 w-5" />
              Analyse de l'Évolution
              {hasData && (
                <Badge variant="outline" className="bg-white/50 border-blue-300 text-blue-700">
                  <Zap className="h-3 w-3 mr-1" />
                  Temps Réel
                </Badge>
              )}
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-6">
            {!hasData ? (
              /* État vide */
              <div className="text-center py-12">
                <div className="bg-white/60 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Calendar className="h-8 w-8 text-blue-500" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Commencez le suivi de l'évolution
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Créez votre premier point d'évolution en figeant le total actuel. 
                  Vous pourrez ensuite suivre l'évolution de votre patrimoine dans le temps.
                </p>
                <Button
                  onClick={freezeCurrentTotal}
                  disabled={loading || currentTotal <= 0}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Créer le Premier Point d'Évolution
                </Button>
                {currentTotal <= 0 && (
                  <p className="text-sm text-amber-600 mt-2">
                    Ajoutez d'abord des montants dans le tableau principal
                  </p>
                )}
              </div>
            ) : (
              /* Graphique et données */
              <EvolutionChartProfessional
                data={chartData}
                selectedFournisseurs={selectedFournisseurs}
                showTotalCurve={showTotalCurve}
                fournisseursDisponibles={fournisseursDisponibles}
                metrics={metrics}
                loading={loading}
                onToggleFournisseur={toggleFournisseur}
                onSelectAll={selectAllFournisseurs}
                onClearSelection={clearSelection}
                onToggleTotalCurve={setShowTotalCurve}
              />
            )}

            {/* Informations sur le temps réel */}
            {hasData && (
              <div className="bg-white/60 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center gap-2 text-sm text-blue-700">
                  <Zap className="h-4 w-4" />
                  <span className="font-medium">Synchronisation Temps Réel Active</span>
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  Les courbes se mettent à jour automatiquement lors des modifications du patrimoine
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
